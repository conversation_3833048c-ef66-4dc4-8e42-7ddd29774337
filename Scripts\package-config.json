{"project": {"name": "SpectrumIDE", "version": "1.0.0", "description": "Advanced IDE for Alif Programming Language with LSP Support", "author": "Alif Development Team", "license": "MIT", "homepage": "https://github.com/alif-lang/spectrum-ide"}, "build": {"output_dir": "dist", "temp_dir": "build", "clean_before_build": true, "parallel_jobs": "auto"}, "components": {"spectrum_ide": {"source_dir": "Spectrum", "build_system": "qmake", "config_file": "Spectrum.pro", "dependencies": ["Qt5Core", "Qt5Widgets", "Qt5Gui"]}, "als_server": {"source_dir": "als", "build_system": "cmake", "config_file": "CMakeLists.txt", "dependencies": ["n<PERSON><PERSON>_json", "spdlog", "fmt"]}}, "platforms": {"windows": {"executable_suffix": ".exe", "library_suffix": ".dll", "package_formats": ["zip", "nsis"], "qt_deploy_tool": "windeployqt", "required_dlls": ["Qt5Core.dll", "Qt5Widgets.dll", "Qt5Gui.dll", "vcruntime140.dll", "msvcp140.dll"], "installer": {"type": "nsis", "script": "Scripts/windows-installer.nsi", "icon": "Resources/TaifLogo.ico"}}, "linux": {"executable_suffix": "", "library_suffix": ".so", "package_formats": ["tar.gz", "appimage", "deb"], "qt_deploy_tool": "linuxdeployqt", "required_libs": ["libQt5Core.so.5", "libQt5Widgets.so.5", "libQt5Gui.so.5", "libstdc++.so.6"], "desktop_entry": {"name": "SpectrumIDE", "comment": "Alif Programming Language IDE", "exec": "spectrum-ide", "icon": "spectrum-ide", "categories": "Development;IDE;"}}, "macos": {"executable_suffix": "", "library_suffix": ".dylib", "package_formats": ["dmg", "zip"], "qt_deploy_tool": "macdeployqt", "app_bundle": {"name": "Spectrum.app", "identifier": "com.alif.spectrum", "version": "1.0.0", "icon": "Resources/TaifLogo.icns"}, "code_signing": {"enabled": false, "identity": "Developer ID Application: Your Name", "entitlements": "Scripts/macos-entitlements.plist"}}}, "distribution": {"include_files": [{"source": "README.md", "destination": "docs/README.md"}, {"source": "Documents/ALS/", "destination": "docs/", "recursive": true}, {"source": "Spectrum/Resources/", "destination": "resources/", "recursive": true}], "exclude_patterns": ["*.pdb", "*.ilk", "*.exp", "*.lib", "*.tmp", "*.log", ".DS_Store", "Thumbs.db"], "launcher_scripts": {"windows": {"file": "launch-spectrum.bat", "template": "Scripts/templates/windows-launcher.bat.template"}, "linux": {"file": "launch-spectrum.sh", "template": "Scripts/templates/linux-launcher.sh.template"}, "macos": {"file": "launch-spectrum.sh", "template": "Scripts/templates/macos-launcher.sh.template"}}}, "testing": {"unit_tests": {"enabled": true, "framework": "QtTest", "test_dir": "Tests", "timeout": 300}, "integration_tests": {"enabled": true, "requires_als_server": true, "test_workspace": "Tests/test_workspace", "timeout": 600}, "manual_tests": {"guide": "Tests/Manual/LSP-Testing-Guide.md", "checklist": "Tests/Manual/test-checklist.md"}}, "deployment": {"staging": {"enabled": true, "upload_url": "https://staging.alif-lang.org/releases/", "credentials_file": ".staging-credentials"}, "production": {"enabled": false, "upload_url": "https://releases.alif-lang.org/spectrum/", "credentials_file": ".production-credentials", "requires_approval": true}, "github_releases": {"enabled": true, "repository": "alif-lang/spectrum-ide", "draft": true, "prerelease": false}}, "quality_assurance": {"code_analysis": {"enabled": true, "tools": ["cppcheck", "clang-tidy"], "fail_on_warnings": false}, "performance_tests": {"enabled": true, "memory_limit_mb": 512, "startup_time_limit_ms": 5000, "large_file_size_mb": 10}, "security_scan": {"enabled": true, "tools": ["bandit", "safety"], "fail_on_medium": false}}}