# SpectrumIDE with ALS Server - Installation Guide

## Overview

This guide provides comprehensive installation instructions for SpectrumIDE with integrated Alif Language Server (ALS) support. The package includes both the IDE and the language server for a complete development experience.

## System Requirements

### Minimum Requirements
- **RAM**: 4 GB (8 GB recommended)
- **Storage**: 500 MB free space
- **Display**: 1024x768 resolution (1920x1080 recommended)

### Platform-Specific Requirements

#### Windows
- **OS**: Windows 10 or later (64-bit)
- **Runtime**: Visual C++ Redistributable 2019 or later
- **Optional**: Windows Terminal for better console experience

#### Linux
- **OS**: Ubuntu 18.04+, CentOS 7+, or equivalent
- **Libraries**: glibc 2.17+, libstdc++6
- **Desktop**: X11 or Wayland display server
- **Optional**: Arabic font packages for better text rendering

#### macOS
- **OS**: macOS 10.14 (Mojave) or later
- **Architecture**: Intel x64 or Apple Silicon (M1/M2)
- **Optional**: Xcode Command Line Tools for development features

## Installation Methods

### Method 1: Pre-built Packages (Recommended)

#### Windows Installation
1. **Download Package**
   ```
   Download: SpectrumIDE-with-ALS-Release-Windows-x64.zip
   ```

2. **Extract and Install**
   ```cmd
   # Extract to desired location (e.g., C:\Program Files\SpectrumIDE)
   # Run as Administrator if installing to Program Files
   ```

3. **Launch Application**
   ```cmd
   # Double-click launch-spectrum.bat
   # Or run from command line:
   cd "C:\Program Files\SpectrumIDE"
   launch-spectrum.bat
   ```

4. **Create Desktop Shortcut** (Optional)
   - Right-click on `launch-spectrum.bat`
   - Select "Create shortcut"
   - Move shortcut to Desktop
   - Rename to "SpectrumIDE"

#### Linux Installation
1. **Download Package**
   ```bash
   wget https://releases.alif-lang.org/spectrum/SpectrumIDE-with-ALS-Release-Linux-x64.tar.gz
   ```

2. **Extract and Install**
   ```bash
   # Extract to /opt (system-wide) or ~/Applications (user-only)
   sudo tar -xzf SpectrumIDE-with-ALS-Release-Linux-x64.tar.gz -C /opt/
   
   # Or for user installation:
   mkdir -p ~/Applications
   tar -xzf SpectrumIDE-with-ALS-Release-Linux-x64.tar.gz -C ~/Applications/
   ```

3. **Set Permissions**
   ```bash
   sudo chmod +x /opt/SpectrumIDE/launch-spectrum.sh
   sudo chmod +x /opt/SpectrumIDE/bin/Spectrum
   sudo chmod +x /opt/SpectrumIDE/bin/als
   ```

4. **Create System Integration** (Optional)
   ```bash
   # Create desktop entry
   sudo cp /opt/SpectrumIDE/spectrum-ide.desktop /usr/share/applications/
   
   # Create symbolic link for command line access
   sudo ln -s /opt/SpectrumIDE/launch-spectrum.sh /usr/local/bin/spectrum-ide
   ```

#### macOS Installation
1. **Download Package**
   ```bash
   curl -O https://releases.alif-lang.org/spectrum/SpectrumIDE-with-ALS-Release-Darwin-x64.dmg
   ```

2. **Install from DMG**
   ```bash
   # Mount DMG
   hdiutil mount SpectrumIDE-with-ALS-Release-Darwin-x64.dmg
   
   # Copy to Applications
   cp -R "/Volumes/SpectrumIDE/Spectrum.app" /Applications/
   
   # Unmount DMG
   hdiutil unmount "/Volumes/SpectrumIDE"
   ```

3. **Handle Security Settings**
   ```bash
   # If blocked by Gatekeeper, run:
   sudo xattr -rd com.apple.quarantine /Applications/Spectrum.app
   
   # Or go to System Preferences > Security & Privacy > Allow
   ```

### Method 2: Build from Source

#### Prerequisites
```bash
# Install build dependencies
# Qt 5.15+ development libraries
# CMake 3.20+
# C++23 compatible compiler (GCC 11+, Clang 14+, MSVC 2022+)
```

#### Build Process
```bash
# Clone repository
git clone https://github.com/alif-lang/spectrum-ide.git
cd spectrum-ide

# Run build script
chmod +x Scripts/build-spectrum-with-als.sh
./Scripts/build-spectrum-with-als.sh

# Install
sudo ./Scripts/build-spectrum-with-als.sh install
```

## Configuration

### First Launch Setup

1. **Language Server Configuration**
   - ALS server path is automatically detected
   - Workspace root defaults to user's Documents folder
   - All LSP features enabled by default

2. **Editor Settings**
   - Font: Kawkab Mono (included)
   - Theme: Dark theme optimized for Arabic text
   - Text direction: Right-to-left (RTL) for Alif code

3. **Arabic Text Support**
   - Automatic RTL text direction
   - Arabic keyboard layout support
   - Proper cursor movement for Arabic text

### Advanced Configuration

#### Custom ALS Server Path
```bash
# Set environment variable (if needed)
export ALS_SERVER_PATH="/custom/path/to/als"

# Or configure in IDE settings:
# Settings > Language Server > ALS Server Path
```

#### Workspace Configuration
```bash
# Create .spectrum-ide folder in project root
mkdir .spectrum-ide

# Create configuration file
cat > .spectrum-ide/config.json << EOF
{
  "lsp": {
    "enabled": true,
    "features": {
      "completion": true,
      "hover": true,
      "diagnostics": true,
      "definition": true
    }
  },
  "editor": {
    "fontSize": 12,
    "tabSize": 4,
    "wordWrap": true
  }
}
EOF
```

## Verification

### Test Installation
1. **Launch SpectrumIDE**
   - Verify application starts without errors
   - Check that ALS server process starts automatically

2. **Create Test File**
   ```alif
   دالة اختبار() {
       اطبع("مرحبا بالعالم")
       متغير عدد = 42
       ارجع عدد
   }
   ```

3. **Verify LSP Features**
   - Syntax highlighting works
   - Code completion appears when typing
   - No error messages in console

### Troubleshooting

#### Common Issues

**Issue**: "ALS server not found"
```bash
# Solution: Verify ALS server path
ls -la /path/to/als/server
# Ensure executable permissions
chmod +x /path/to/als/server
```

**Issue**: "Connection timeout"
```bash
# Solution: Check firewall/antivirus
# Verify ALS server can start independently
./als --version
```

**Issue**: "Arabic text not displaying correctly"
```bash
# Linux: Install Arabic fonts
sudo apt-get install fonts-arabeyes fonts-kacst

# macOS: Install Arabic font support
# Windows: Enable Arabic language pack
```

**Issue**: "Qt library errors"
```bash
# Linux: Install Qt runtime
sudo apt-get install qt5-default

# Check library dependencies
ldd /path/to/Spectrum
```

## Development Environment Setup

### For Contributors

1. **Install Development Tools**
   ```bash
   # Qt Creator (recommended IDE)
   # CMake GUI (for ALS server)
   # Git (version control)
   # Debugger (gdb/lldb/Visual Studio)
   ```

2. **Setup Development Workspace**
   ```bash
   git clone https://github.com/alif-lang/spectrum-ide.git
   cd spectrum-ide
   git submodule update --init --recursive
   ```

3. **Configure IDE**
   - Import Spectrum.pro into Qt Creator
   - Set up build configurations (Debug/Release)
   - Configure debugger for both IDE and ALS server

### Testing Environment

1. **Unit Tests**
   ```bash
   cd Tests
   qmake Tests.pro
   make
   ./TestRunner
   ```

2. **Integration Tests**
   ```bash
   # Ensure ALS server is built
   cd Tests/Integration
   ./run-integration-tests.sh
   ```

3. **Manual Testing**
   - Follow guide in `Tests/Manual/LSP-Testing-Guide.md`
   - Test with various Arabic text samples
   - Verify all LSP features work correctly

## Support

### Getting Help
- **Documentation**: `docs/` folder in installation directory
- **Issues**: https://github.com/alif-lang/spectrum-ide/issues
- **Discussions**: https://github.com/alif-lang/spectrum-ide/discussions
- **Email**: <EMAIL>

### Reporting Bugs
Include the following information:
- Operating system and version
- SpectrumIDE version
- Steps to reproduce
- Expected vs actual behavior
- Log files (if available)

### Contributing
- Read `CONTRIBUTING.md` for guidelines
- Follow coding standards in `docs/coding-standards.md`
- Submit pull requests with tests
- Update documentation as needed

---

**Installation Guide Version**: 1.0  
**Last Updated**: January 2025  
**Compatible with**: SpectrumIDE 1.0.0 + ALS Server 1.0.0
