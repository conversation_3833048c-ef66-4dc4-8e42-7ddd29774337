cc_library(
    name = "fmt",
    srcs = [
        #"src/fmt.cc", # No C++ module support
        "src/format.cc",
        "src/os.cc",
    ],
    hdrs = [
        "include/fmt/args.h",
        "include/fmt/chrono.h",
        "include/fmt/color.h",
        "include/fmt/compile.h",
        "include/fmt/core.h",
        "include/fmt/format.h",
        "include/fmt/format-inl.h",
        "include/fmt/os.h",
        "include/fmt/ostream.h",
        "include/fmt/printf.h",
        "include/fmt/ranges.h",
        "include/fmt/std.h",
        "include/fmt/xchar.h",
    ],
    includes = [
        "include",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
)
