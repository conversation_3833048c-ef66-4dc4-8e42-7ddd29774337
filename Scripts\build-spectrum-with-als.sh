#!/bin/bash

# SpectrumIDE with ALS Server Build Script
# This script builds both SpectrumIDE and ALS server for distribution

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_TYPE="${BUILD_TYPE:-Release}"
PLATFORM="$(uname -s)"
ARCH="$(uname -m)"

# Output directories
BUILD_DIR="$PROJECT_ROOT/build"
DIST_DIR="$PROJECT_ROOT/dist"
ALS_BUILD_DIR="$PROJECT_ROOT/als/build"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking build prerequisites..."
    
    # Check for required tools
    local missing_tools=()
    
    if ! command -v cmake &> /dev/null; then
        missing_tools+=("cmake")
    fi
    
    if ! command -v qmake &> /dev/null; then
        missing_tools+=("qmake (Qt development tools)")
    fi
    
    if [[ "$PLATFORM" == "Linux" ]] && ! command -v make &> /dev/null; then
        missing_tools+=("make")
    fi
    
    if [[ "$PLATFORM" == "Darwin" ]] && ! command -v xcodebuild &> /dev/null; then
        missing_tools+=("xcodebuild (Xcode command line tools)")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        exit 1
    fi
    
    log_success "All prerequisites satisfied"
}

# Build ALS server
build_als_server() {
    log_info "Building ALS server..."
    
    cd "$PROJECT_ROOT/als"
    
    # Create build directory
    mkdir -p "$ALS_BUILD_DIR"
    cd "$ALS_BUILD_DIR"
    
    # Configure with CMake
    cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
          -DCMAKE_INSTALL_PREFIX="$DIST_DIR/als" \
          ..
    
    # Build
    cmake --build . --config "$BUILD_TYPE" --parallel $(nproc 2>/dev/null || echo 4)
    
    # Install to distribution directory
    cmake --install . --config "$BUILD_TYPE"
    
    log_success "ALS server built successfully"
}

# Build SpectrumIDE
build_spectrum_ide() {
    log_info "Building SpectrumIDE..."
    
    cd "$PROJECT_ROOT/Spectrum"
    
    # Create build directory
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # Configure with qmake
    qmake ../Spectrum.pro CONFIG+="$BUILD_TYPE"
    
    # Build
    if [[ "$PLATFORM" == "Darwin" ]]; then
        make -j$(sysctl -n hw.ncpu)
    else
        make -j$(nproc)
    fi
    
    log_success "SpectrumIDE built successfully"
}

# Package for distribution
package_distribution() {
    log_info "Creating distribution package..."
    
    # Create distribution structure
    mkdir -p "$DIST_DIR/bin"
    mkdir -p "$DIST_DIR/lib"
    mkdir -p "$DIST_DIR/resources"
    mkdir -p "$DIST_DIR/docs"
    
    # Copy SpectrumIDE executable
    if [[ "$PLATFORM" == "Darwin" ]]; then
        cp -r "$BUILD_DIR/Spectrum.app" "$DIST_DIR/"
        # Copy ALS server into app bundle
        mkdir -p "$DIST_DIR/Spectrum.app/Contents/MacOS/als"
        cp "$DIST_DIR/als/bin/als" "$DIST_DIR/Spectrum.app/Contents/MacOS/als/"
    elif [[ "$PLATFORM" == "MINGW"* ]] || [[ "$PLATFORM" == "CYGWIN"* ]]; then
        cp "$BUILD_DIR/Spectrum.exe" "$DIST_DIR/bin/"
        cp "$DIST_DIR/als/bin/als.exe" "$DIST_DIR/bin/"
    else
        cp "$BUILD_DIR/Spectrum" "$DIST_DIR/bin/"
        cp "$DIST_DIR/als/bin/als" "$DIST_DIR/bin/"
    fi
    
    # Copy Qt libraries (platform-specific)
    copy_qt_libraries
    
    # Copy resources
    cp -r "$PROJECT_ROOT/Spectrum/Resources"/* "$DIST_DIR/resources/" 2>/dev/null || true
    
    # Copy documentation
    cp "$PROJECT_ROOT/README.md" "$DIST_DIR/docs/"
    cp -r "$PROJECT_ROOT/Documents/ALS"/* "$DIST_DIR/docs/" 2>/dev/null || true
    
    # Create launcher scripts
    create_launcher_scripts
    
    log_success "Distribution package created in $DIST_DIR"
}

# Copy Qt libraries (platform-specific)
copy_qt_libraries() {
    log_info "Copying Qt libraries..."
    
    if [[ "$PLATFORM" == "Linux" ]]; then
        # Use linuxdeployqt or similar tool
        if command -v linuxdeployqt &> /dev/null; then
            linuxdeployqt "$DIST_DIR/bin/Spectrum" -qmldir="$PROJECT_ROOT/Spectrum"
        else
            log_warning "linuxdeployqt not found. Manual library copying required."
        fi
    elif [[ "$PLATFORM" == "Darwin" ]]; then
        # Use macdeployqt
        if command -v macdeployqt &> /dev/null; then
            macdeployqt "$DIST_DIR/Spectrum.app"
        else
            log_warning "macdeployqt not found. Manual library copying required."
        fi
    elif [[ "$PLATFORM" == "MINGW"* ]] || [[ "$PLATFORM" == "CYGWIN"* ]]; then
        # Use windeployqt
        if command -v windeployqt &> /dev/null; then
            windeployqt "$DIST_DIR/bin/Spectrum.exe"
        else
            log_warning "windeployqt not found. Manual library copying required."
        fi
    fi
}

# Create launcher scripts
create_launcher_scripts() {
    log_info "Creating launcher scripts..."
    
    if [[ "$PLATFORM" == "Darwin" ]]; then
        # macOS launcher
        cat > "$DIST_DIR/launch-spectrum.sh" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export ALS_SERVER_PATH="$SCRIPT_DIR/Spectrum.app/Contents/MacOS/als/als"
open "$SCRIPT_DIR/Spectrum.app"
EOF
    elif [[ "$PLATFORM" == "MINGW"* ]] || [[ "$PLATFORM" == "CYGWIN"* ]]; then
        # Windows launcher
        cat > "$DIST_DIR/launch-spectrum.bat" << 'EOF'
@echo off
set SCRIPT_DIR=%~dp0
set ALS_SERVER_PATH=%SCRIPT_DIR%bin\als.exe
start "" "%SCRIPT_DIR%bin\Spectrum.exe"
EOF
    else
        # Linux launcher
        cat > "$DIST_DIR/launch-spectrum.sh" << 'EOF'
#!/bin/bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export ALS_SERVER_PATH="$SCRIPT_DIR/bin/als"
export LD_LIBRARY_PATH="$SCRIPT_DIR/lib:$LD_LIBRARY_PATH"
"$SCRIPT_DIR/bin/Spectrum"
EOF
    fi
    
    chmod +x "$DIST_DIR"/launch-spectrum.*
}

# Create installation package
create_installer() {
    log_info "Creating installation package..."
    
    local package_name="SpectrumIDE-with-ALS-${BUILD_TYPE}-${PLATFORM}-${ARCH}"
    local package_dir="$PROJECT_ROOT/packages"
    
    mkdir -p "$package_dir"
    
    if [[ "$PLATFORM" == "Linux" ]]; then
        # Create AppImage or tar.gz
        cd "$DIST_DIR"
        tar -czf "$package_dir/${package_name}.tar.gz" .
        log_success "Created Linux package: ${package_name}.tar.gz"
        
    elif [[ "$PLATFORM" == "Darwin" ]]; then
        # Create DMG
        if command -v create-dmg &> /dev/null; then
            create-dmg \
                --volname "SpectrumIDE" \
                --window-pos 200 120 \
                --window-size 600 300 \
                --icon-size 100 \
                --app-drop-link 425 120 \
                "$package_dir/${package_name}.dmg" \
                "$DIST_DIR"
            log_success "Created macOS package: ${package_name}.dmg"
        else
            # Fallback to zip
            cd "$DIST_DIR"
            zip -r "$package_dir/${package_name}.zip" .
            log_success "Created macOS package: ${package_name}.zip"
        fi
        
    elif [[ "$PLATFORM" == "MINGW"* ]] || [[ "$PLATFORM" == "CYGWIN"* ]]; then
        # Create NSIS installer or zip
        cd "$DIST_DIR"
        zip -r "$package_dir/${package_name}.zip" .
        log_success "Created Windows package: ${package_name}.zip"
    fi
}

# Main build process
main() {
    log_info "Starting SpectrumIDE with ALS build process..."
    log_info "Platform: $PLATFORM, Architecture: $ARCH, Build Type: $BUILD_TYPE"
    
    # Clean previous builds if requested
    if [[ "$1" == "clean" ]]; then
        log_info "Cleaning previous builds..."
        rm -rf "$BUILD_DIR" "$DIST_DIR" "$ALS_BUILD_DIR"
    fi
    
    # Execute build steps
    check_prerequisites
    build_als_server
    build_spectrum_ide
    package_distribution
    create_installer
    
    log_success "Build completed successfully!"
    log_info "Distribution package available in: $DIST_DIR"
    log_info "Installation package available in: $PROJECT_ROOT/packages"
}

# Run main function with all arguments
main "$@"
