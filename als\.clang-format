# Alif Language Server - Code Formatting Configuration
# Based on LLVM style with customizations for modern C++

BasedOnStyle: LLVM

# Language and Standard
Language: Cpp
Standard: c++23

# Indentation
IndentWidth: 4
TabWidth: 4
UseTab: Never
ContinuationIndentWidth: 4
IndentCaseLabels: true
IndentPPDirectives: BeforeHash
IndentWrappedFunctionNames: false
IndentAccessModifiers: false
AccessModifierOffset: -2

# Line Length and Breaking
ColumnLimit: 100
BreakBeforeBraces: Attach
BreakBeforeTernaryOperators: true
BreakConstructorInitializers: BeforeColon
BreakInheritanceList: BeforeColon
BreakStringLiterals: true
AlwaysBreakAfterReturnType: None
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakBeforeMultilineStrings: false

# Spacing
SpaceAfterCStyleCast: false
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCpp11BracedList: false
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeRangeBasedForLoopColon: true
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 2
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInContainerLiterals: true
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Alignment
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignConsecutiveMacros: false
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true

# Sorting and Includes
SortIncludes: true
SortUsingDeclarations: true
IncludeBlocks: Regroup
IncludeCategories:
  - Regex: '^<.*\.h>'
    Priority: 1
  - Regex: '^<.*>'
    Priority: 2
  - Regex: '^".*"'
    Priority: 3

# Function and Class Formatting
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortBlocksOnASingleLine: Empty
AllowAllParametersOfDeclarationOnNextLine: true
BinPackArguments: true
BinPackParameters: true

# Pointer and Reference Alignment
PointerAlignment: Left
ReferenceAlignment: Left

# Modern C++ Features
Cpp11BracedListStyle: true
FixNamespaceComments: true
CompactNamespaces: false
NamespaceIndentation: None

# Comments
ReflowComments: true
CommentPragmas: '^ IWYU pragma:'

# Penalties (for line breaking decisions)
PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 60

# Experimental features
ExperimentalAutoDetectBinPacking: false
