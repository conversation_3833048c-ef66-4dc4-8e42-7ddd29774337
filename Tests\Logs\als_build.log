-- Selecting Windows SDK version 10.0.26100.0 to target Windows 10.0.19045.
-- n<PERSON><PERSON>_json not found via find_package, trying pkg-config
-- n<PERSON><PERSON>_json not found, will use bundled version
-- spdlog not found via find_package, trying pkg-config
-- spdlog not found, will use bundled version
-- fmt not found via find_package, trying pkg-config
-- fmt not found, will use bundled version
-- nlohmann_json already exists, skipping download
-- Using bundled nlohmann/json
-- Build spdlog: 1.12.0
-- Build type: Debug
-- Using bundled spdlog
-- Version: 10.1.0
-- Build type: Debug
-- Using bundled fmt
-- Third-party dependencies configured
-- Configuring ALS tests...
-- Google Test not found, using basic test runner
-- ALS tests configured successfully
-- 
-- === Alif Language Server Configuration ===
-- Version: 1.0.0
-- Build type: Debug
-- C++ standard: 23
-- Compiler: MSVC 19.44.35211.0
-- Install prefix: D:/dev/SpectrumIDEV3/dist/als
-- 
-- Dependencies:
--   nlo<PERSON>_json: 0
--   spdlog: 0
--   fmt: 0
-- 
-- Options:
--   Build tests: ON
--   Build benchmarks: OFF
-- ==========================================
-- 
-- Configuring done (0.8s)
-- Generating done (0.8s)
-- Build files have been written to: D:/dev/SpectrumIDEV3/als/build
MSBuild version 17.14.10+8b8e13593 for .NET Framework

  fmt.vcxproj -> D:\dev\SpectrumIDEV3\als\build\third_party\fmt\Debug\fmtd.lib
  spdlog.vcxproj -> D:\dev\SpectrumIDEV3\als\build\third_party\spdlog\Debug\spdlogd.lib
  als.vcxproj -> D:\dev\SpectrumIDEV3\als\build\Debug\alif-language-server.exe
  als_dispatcher_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_dispatcher_tests.exe
  als_jsonrpc_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_jsonrpc_tests.exe
  als_logging_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_logging_tests.exe
  als_lsp_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_lsp_tests.exe
  als_protocol_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_protocol_tests.exe
  als_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_tests.exe
  als_threadpool_tests.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\als_threadpool_tests.exe
  debug_stream.vcxproj -> D:\dev\SpectrumIDEV3\als\build\tests\Debug\debug_stream.exe
