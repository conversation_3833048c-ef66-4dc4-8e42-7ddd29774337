# SpectrumIDE LSP Client - Comprehensive Testing Guide

## Overview

This guide provides complete step-by-step instructions for testing the SpectrumIDE LSP client integration, starting from scratch. Follow each section in order to ensure proper testing coverage.

## 1. Prerequisites Setup

### 1.1 Required Tools and Dependencies

#### Essential Development Tools
```bash
# Check if tools are installed
qt5-qmake --version    # Should show Qt 5.15+ 
cmake --version        # Should show CMake 3.20+
gcc --version          # Should show GCC 11+ (or equivalent compiler)
git --version          # For version control

# If missing, install based on your platform:
```

**Windows (using vcpkg or Qt installer):**
```cmd
# Install Qt 5.15+ from https://www.qt.io/download
# Install Visual Studio 2022 with C++ support
# Install CMake from https://cmake.org/download/
# Install Git from https://git-scm.com/download/win
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install -y qt5-qmake qt5-default qtcreator cmake build-essential git
sudo apt install -y qtbase5-dev qttools5-dev-tools
sudo apt install -y libqt5test5 qt5-doc qt5-doc-html qtbase5-examples
```

**macOS:**
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Qt using Homebrew
brew install qt@5 cmake git
echo 'export PATH="/usr/local/opt/qt@5/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

#### Testing-Specific Tools
```bash
# Install additional testing tools
pip3 install pytest pytest-qt  # For Python-based testing (optional)

# Linux: Install debugging tools
sudo apt install -y gdb valgrind strace

# macOS: Install debugging tools  
brew install lldb

# Windows: Visual Studio includes debugging tools
```

### 1.2 Environment Setup

#### Create Testing Directory Structure
```bash
# Navigate to your SpectrumIDEV3 directory
cd /path/to/SpectrumIDEV3

# Create testing directories
mkdir -p Tests/{Unit,Integration,Manual,Results,Logs}
mkdir -p Tests/TestData/{AlifFiles,Workspaces}
mkdir -p Tests/MockServers

# Set environment variables
export SPECTRUM_TEST_ROOT="$(pwd)/Tests"
export SPECTRUM_BUILD_DIR="$(pwd)/build"
export ALS_SERVER_PATH="$(pwd)/als/build/als"  # Adjust path as needed

# Create test configuration file
cat > Tests/test-config.json << 'EOF'
{
  "test_environment": {
    "spectrum_root": ".",
    "als_server_path": "./als/build/als",
    "test_workspace": "./Tests/TestData/Workspaces/test_workspace",
    "log_level": "debug",
    "timeout_seconds": 30
  },
  "test_categories": {
    "unit_tests": true,
    "integration_tests": true,
    "manual_tests": true,
    "performance_tests": false
  }
}
EOF
```

#### Create Test Workspace
```bash
# Create test workspace with sample Alif files
mkdir -p Tests/TestData/Workspaces/test_workspace

# Create basic Alif test file
cat > Tests/TestData/Workspaces/test_workspace/basic.alif << 'EOF'
دالة اختبار() {
    اطبع("مرحبا بالعالم")
    متغير عدد = 42
    ارجع عدد
}

صنف مثال {
    دالة _تهيئة_(اسم) {
        هذا.اسم = اسم
    }
    
    دالة عرض() {
        اطبع("الاسم: " + هذا.اسم)
    }
}
EOF

# Create Arabic text test file
cat > Tests/TestData/Workspaces/test_workspace/arabic_test.alif << 'EOF'
// اختبار النص العربي والاتجاه من اليمين إلى اليسار
دالة معالجة_النص_العربي() {
    متغير نص = "السلام عليكم ورحمة الله وبركاته"
    متغير أرقام = [١، ٢، ٣، ٤، ٥]
    
    لاجل رقم في أرقام {
        اطبع("الرقم: " + رقم)
    }
    
    ارجع نص
}

// Mixed RTL/LTR content test
دالة mixed_content_test() {
    متغير result = calculateSum(10, 20)
    اطبع("النتيجة: " + result)
    return result
}
EOF

# Create large file for performance testing
python3 -c "
content = '''دالة اختبار_{}() {{
    متغير قيمة = {}
    اطبع(\"اختبار رقم: {}\")
    ارجع قيمة
}}

'''
with open('Tests/TestData/Workspaces/test_workspace/large_file.alif', 'w', encoding='utf-8') as f:
    for i in range(1000):
        f.write(content.format(i, i*2, i))
"
```

## 2. Build Verification

### 2.1 Clean Build Process

#### Step 1: Clean Previous Builds
```bash
# Remove any existing build artifacts
rm -rf build/
rm -rf Spectrum/build/
rm -rf als/build/

# Clean Qt build files
find . -name "*.o" -delete
find . -name "moc_*" -delete
find . -name "ui_*" -delete
find . -name "qrc_*" -delete
find . -name "Makefile*" -delete
```

#### Step 2: Build ALS Server First
```bash
# Navigate to ALS directory
cd als

# Create build directory
mkdir build && cd build

# Configure with CMake (Debug build for testing)
cmake -DCMAKE_BUILD_TYPE=Debug \
      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
      -DALS_ENABLE_TESTING=ON \
      ..

# Build ALS server
cmake --build . --parallel $(nproc 2>/dev/null || echo 4)

# Verify ALS server built successfully
ls -la als*  # Should show als executable
./als --version  # Should show version information

# Test ALS server basic functionality
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"processId":null,"rootUri":"file:///tmp","capabilities":{}}}' | ./als --stdio
# Should respond with initialize result (Ctrl+C to exit)

cd ../..  # Return to project root
```

#### Step 3: Build SpectrumIDE with LSP Client
```bash
# Navigate to Spectrum directory
cd Spectrum

# Create build directory
mkdir -p build && cd build

# Configure with qmake
qmake ../Spectrum.pro CONFIG+=debug

# Verify project file includes LSP client files
grep -n "LspClient" ../Spectrum.pro
# Should show lines including LSP client source and header files

# Build SpectrumIDE
make -j$(nproc 2>/dev/null || echo 4)

# Check for compilation errors
echo $?  # Should be 0 (success)

# Verify executable was created
ls -la Spectrum*  # Should show Spectrum executable

# Check LSP client symbols are included
nm Spectrum | grep -i lsp  # Should show LSP-related symbols
# Or on macOS: nm Spectrum | grep -i lsp

cd ../..  # Return to project root
```

### 2.2 Verify Integration

#### Check Header Dependencies
```bash
# Verify all LSP client headers can be found
cd Source/LspClient

# Test header compilation individually
for header in *.h; do
    echo "Testing $header..."
    echo "#include \"$header\"" > test_include.cpp
    echo "int main() { return 0; }" >> test_include.cpp
    
    g++ -I. -I../TextEditor -I../MenuBar -I../Settings -I../Components \
        $(pkg-config --cflags Qt5Core Qt5Widgets) \
        -c test_include.cpp -o test_include.o
    
    if [ $? -eq 0 ]; then
        echo "✅ $header compiles successfully"
    else
        echo "❌ $header has compilation errors"
    fi
    
    rm -f test_include.cpp test_include.o
done

cd ../..
```

#### Verify Qt MOC Generation
```bash
# Check that Qt's Meta-Object Compiler processed our files
cd Spectrum/build

# Look for generated MOC files
ls -la moc_*LspClient* moc_*LspProcess* moc_*LspProtocol*
# Should show moc_SpectrumLspClient.cpp, moc_LspProcess.cpp, etc.

# Verify MOC files contain expected content
grep -n "Q_OBJECT" moc_SpectrumLspClient.cpp
# Should show Q_OBJECT macro processing

cd ../..
```

## 3. Unit Testing

### 3.1 Create Test Runner

#### Create Main Test Runner
```bash
# Create test runner project file
cat > Tests/Unit/UnitTests.pro << 'EOF'
QT += core widgets testlib
CONFIG += testcase

TARGET = UnitTestRunner
TEMPLATE = app

# Include paths
INCLUDEPATH += ../../Source/LspClient \
               ../../Source/TextEditor \
               ../../Source/MenuBar \
               ../../Source/Settings \
               ../../Source/Components

# Source files under test
SOURCES += ../../Source/LspClient/SpectrumLspClient.cpp \
           ../../Source/LspClient/LspProcess.cpp \
           ../../Source/LspClient/LspProtocol.cpp \
           ../../Source/LspClient/LspFeatureManager.cpp \
           ../../Source/LspClient/DocumentManager.cpp

# Test files
SOURCES += TestSpectrumLspClient.cpp \
           TestLspProcess.cpp \
           TestLspProtocol.cpp \
           TestMain.cpp

# Headers
HEADERS += ../../Source/LspClient/SpectrumLspClient.h \
           ../../Source/LspClient/LspProcess.h \
           ../../Source/LspClient/LspProtocol.h \
           ../../Source/LspClient/LspFeatureManager.h \
           ../../Source/LspClient/DocumentManager.h
EOF
```

#### Create Test Main Function
```cpp
// Tests/Unit/TestMain.cpp
#include <QtTest>
#include <QApplication>
#include <QDir>

// Include test classes
#include "TestSpectrumLspClient.h"
#include "TestLspProcess.h" 
#include "TestLspProtocol.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set up test environment
    QDir::setCurrent(QCoreApplication::applicationDirPath());
    
    int result = 0;
    
    // Run all test classes
    {
        TestSpectrumLspClient test;
        result |= QTest::qExec(&test, argc, argv);
    }
    
    {
        TestLspProcess test;
        result |= QTest::qExec(&test, argc, argv);
    }
    
    {
        TestLspProtocol test;
        result |= QTest::qExec(&test, argc, argv);
    }
    
    return result;
}
```

### 3.2 Create Mock ALS Server for Testing

#### Create Simple Mock Server
```bash
# Create mock server script
cat > Tests/MockServers/mock_als_server.py << 'EOF'
#!/usr/bin/env python3
"""
Mock ALS Server for Testing
Responds to basic LSP requests for testing purposes
"""

import sys
import json
import threading
import time

class MockALSServer:
    def __init__(self):
        self.running = True
        self.request_id = 0
        
    def read_message(self):
        """Read LSP message from stdin"""
        # Read Content-Length header
        while True:
            line = sys.stdin.readline()
            if line.startswith('Content-Length:'):
                length = int(line.split(':')[1].strip())
                break
        
        # Read empty line
        sys.stdin.readline()
        
        # Read message content
        content = sys.stdin.read(length)
        return json.loads(content)
    
    def send_message(self, message):
        """Send LSP message to stdout"""
        content = json.dumps(message, ensure_ascii=False)
        content_bytes = content.encode('utf-8')
        
        header = f"Content-Length: {len(content_bytes)}\r\n\r\n"
        sys.stdout.write(header)
        sys.stdout.write(content)
        sys.stdout.flush()
    
    def handle_initialize(self, request):
        """Handle initialize request"""
        response = {
            "jsonrpc": "2.0",
            "id": request["id"],
            "result": {
                "capabilities": {
                    "textDocumentSync": 1,
                    "completionProvider": {
                        "triggerCharacters": ["."]
                    },
                    "hoverProvider": True,
                    "definitionProvider": True
                },
                "serverInfo": {
                    "name": "Mock ALS Server",
                    "version": "1.0.0-test"
                }
            }
        }
        self.send_message(response)
    
    def run(self):
        """Main server loop"""
        try:
            while self.running:
                try:
                    message = self.read_message()
                    
                    if message.get("method") == "initialize":
                        self.handle_initialize(message)
                    elif message.get("method") == "initialized":
                        # Just acknowledge
                        pass
                    elif message.get("method") == "shutdown":
                        self.running = False
                        response = {
                            "jsonrpc": "2.0",
                            "id": message["id"],
                            "result": None
                        }
                        self.send_message(response)
                    
                except (EOFError, KeyboardInterrupt):
                    break
                except Exception as e:
                    # Send error response
                    if "id" in message:
                        error_response = {
                            "jsonrpc": "2.0",
                            "id": message["id"],
                            "error": {
                                "code": -32603,
                                "message": str(e)
                            }
                        }
                        self.send_message(error_response)
        
        except Exception:
            pass

if __name__ == "__main__":
    server = MockALSServer()
    server.run()
EOF

chmod +x Tests/MockServers/mock_als_server.py

# Test mock server
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"processId":null,"rootUri":"file:///tmp","capabilities":{}}}' | python3 Tests/MockServers/mock_als_server.py
# Should respond with initialize result
```

### 3.3 Run Unit Tests

#### Build and Execute Tests
```bash
# Navigate to unit tests directory
cd Tests/Unit

# Build tests
qmake UnitTests.pro CONFIG+=debug
make

# Run tests with verbose output
./UnitTestRunner -v2 -o results.xml,xunitxml -o -,txt

# Check test results
echo "Test exit code: $?"  # Should be 0 for success

# View detailed results
cat results.xml  # XML format results
```

#### Interpret Test Results
```bash
# Expected output format:
# ********* Start testing of TestSpectrumLspClient *********
# Config: Using QtTest library 5.15.2
# PASS   : TestSpectrumLspClient::initTestCase()
# PASS   : TestSpectrumLspClient::testSingletonPattern()
# PASS   : TestSpectrumLspClient::testInitialization()
# PASS   : TestSpectrumLspClient::cleanupTestCase()
# Totals: 3 passed, 0 failed, 0 skipped, 0 blacklisted, 2ms

# Look for:
# ✅ PASS - Test passed successfully
# ❌ FAIL - Test failed (investigate)
# ⚠️  SKIP - Test skipped (may be expected)
# 🚫 BLACKLISTED - Test disabled (check why)
```

## 4. Integration Testing

### 4.1 Setup Integration Test Environment

#### Create Integration Test Project
```bash
# Create integration test directory structure
mkdir -p Tests/Integration/{src,build}

# Create integration test project file
cat > Tests/Integration/IntegrationTests.pro << 'EOF'
QT += core widgets testlib
CONFIG += testcase

TARGET = IntegrationTestRunner
TEMPLATE = app

# Include paths (same as main project)
INCLUDEPATH += ../../Source/LspClient \
               ../../Source/TextEditor \
               ../../Source/MenuBar \
               ../../Source/Settings \
               ../../Source/Components

# Source files (include actual implementation)
SOURCES += ../../Source/LspClient/SpectrumLspClient.cpp \
           ../../Source/LspClient/LspProcess.cpp \
           ../../Source/LspClient/LspProtocol.cpp \
           ../../Source/LspClient/LspFeatureManager.cpp \
           ../../Source/LspClient/DocumentManager.cpp \
           ../../Source/TextEditor/SPEditor.cpp \
           ../../Source/TextEditor/SPHighlighter.cpp \
           ../../Source/TextEditor/AlifLexer.cpp

# Test files
SOURCES += src/TestLspIntegration.cpp \
           src/TestArabicHandling.cpp \
           src/IntegrationTestMain.cpp

# Headers
HEADERS += ../../Source/LspClient/SpectrumLspClient.h \
           ../../Source/LspClient/LspProcess.h \
           ../../Source/LspClient/LspProtocol.h \
           ../../Source/LspClient/LspFeatureManager.h \
           ../../Source/LspClient/DocumentManager.h \
           ../../Source/TextEditor/SPEditor.h \
           ../../Source/TextEditor/SPHighlighter.h \
           ../../Source/TextEditor/AlifLexer.h
EOF
```

#### Create Integration Test Implementation
```cpp
// Tests/Integration/src/TestLspIntegration.cpp
#include <QtTest>
#include <QApplication>
#include <QProcess>
#include <QTemporaryDir>
#include <QSignalSpy>
#include <QTimer>
#include <QDebug>

#include "SpectrumLspClient.h"

class TestLspIntegration : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Core integration tests
    void testServerConnection();
    void testInitializeHandshake();
    void testServerRestart();
    void testConnectionTimeout();

private:
    QString findMockServer();
    QString findRealALSServer();
    void waitForConnection(int timeout = 10000);

private:
    QString m_mockServerPath;
    QString m_alsServerPath;
    QTemporaryDir m_testWorkspace;
    SpectrumLspClient* m_client;
};

void TestLspIntegration::initTestCase()
{
    qDebug() << "Setting up integration test environment...";

    // Find mock server
    m_mockServerPath = findMockServer();
    QVERIFY2(!m_mockServerPath.isEmpty(), "Mock server not found");

    // Try to find real ALS server (optional)
    m_alsServerPath = findRealALSServer();
    if (m_alsServerPath.isEmpty()) {
        qWarning() << "Real ALS server not found, using mock server only";
    }

    // Verify test workspace
    QVERIFY(m_testWorkspace.isValid());
    qDebug() << "Test workspace:" << m_testWorkspace.path();
}

void TestLspIntegration::cleanupTestCase()
{
    qDebug() << "Cleaning up integration test environment...";
}

void TestLspIntegration::init()
{
    m_client = &SpectrumLspClient::instance();
    QVERIFY(m_client != nullptr);
}

void TestLspIntegration::cleanup()
{
    if (m_client && m_client->isConnected()) {
        m_client->stop();

        // Wait for disconnection
        QSignalSpy disconnectSpy(m_client, &SpectrumLspClient::connectionStateChanged);
        if (m_client->getConnectionState() != SpectrumLspClient::ConnectionState::Disconnected) {
            QVERIFY(disconnectSpy.wait(5000));
        }
    }
}

void TestLspIntegration::testServerConnection()
{
    qDebug() << "Testing server connection with mock server...";

    // Setup signal spies
    QSignalSpy stateSpy(m_client, &SpectrumLspClient::connectionStateChanged);
    QSignalSpy readySpy(m_client, &SpectrumLspClient::serverReady);
    QSignalSpy errorSpy(m_client, &SpectrumLspClient::errorOccurred);

    // Initialize client
    bool initResult = m_client->initialize(m_mockServerPath, m_testWorkspace.path());
    QVERIFY2(initResult, "Failed to initialize LSP client");

    // Start connection
    bool startResult = m_client->start();
    QVERIFY2(startResult, "Failed to start LSP client");

    // Wait for connection
    QVERIFY2(readySpy.wait(10000), "Server did not become ready within timeout");

    // Verify final state
    QCOMPARE(m_client->getConnectionState(), SpectrumLspClient::ConnectionState::Connected);
    QVERIFY(m_client->isConnected());

    // Check that we received expected state changes
    QVERIFY(stateSpy.count() >= 2); // At least Connecting -> Connected

    // Verify no errors occurred
    if (errorSpy.count() > 0) {
        for (int i = 0; i < errorSpy.count(); ++i) {
            qWarning() << "Error during connection:" << errorSpy.at(i).at(0).toString();
        }
        QFAIL("Errors occurred during connection");
    }

    qDebug() << "✅ Server connection test passed";
}

void TestLspIntegration::testInitializeHandshake()
{
    qDebug() << "Testing LSP initialize handshake...";

    // Connect to server first
    QVERIFY(m_client->initialize(m_mockServerPath, m_testWorkspace.path()));
    QVERIFY(m_client->start());

    QSignalSpy capabilitiesSpy(m_client, &SpectrumLspClient::serverCapabilitiesReceived);
    waitForConnection();

    // Verify capabilities were received
    QVERIFY2(capabilitiesSpy.count() > 0, "Server capabilities not received");

    // Check capabilities
    auto capabilities = m_client->getServerCapabilities();
    QVERIFY(capabilities.textDocumentSync);
    QVERIFY(capabilities.completionProvider);

    qDebug() << "✅ Initialize handshake test passed";
}

QString TestLspIntegration::findMockServer()
{
    QStringList searchPaths = {
        "../../Tests/MockServers/mock_als_server.py",
        "../MockServers/mock_als_server.py",
        "./Tests/MockServers/mock_als_server.py"
    };

    for (const QString& path : searchPaths) {
        if (QFile::exists(path)) {
            // Return python command + script path
            return QString("python3 %1").arg(QFileInfo(path).absoluteFilePath());
        }
    }

    return QString();
}

QString TestLspIntegration::findRealALSServer()
{
    QStringList searchPaths = {
        "../../als/build/als",
        "../../als/build/Debug/als.exe",
        "../../als/build/Release/als.exe",
        "../als/build/als"
    };

    for (const QString& path : searchPaths) {
        if (QFile::exists(path)) {
            return QFileInfo(path).absoluteFilePath();
        }
    }

    return QString();
}

void TestLspIntegration::waitForConnection(int timeout)
{
    if (!m_client->isConnected()) {
        QSignalSpy readySpy(m_client, &SpectrumLspClient::serverReady);
        QVERIFY2(readySpy.wait(timeout), "Failed to connect within timeout");
    }
}

#include "TestLspIntegration.moc"
```
