{% extends "!layout.html" %}

{% block extrahead %}
<meta name="description" content="Small, safe and fast formatting library">
<meta name="keywords" content="C++, formatting, printf, string, library">
<meta name="author" content="<PERSON>">
<link rel="stylesheet" href="_static/fmt.css">
{# Google Analytics #}
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-20116650-4"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-20116650-4');
</script>
{% endblock %}

{%- macro searchform(classes, button) %}
<form class="{{classes}}" role="search" action="{{ pathto('search') }}"
      method="get">
  <div class="form-group">
    <input type="text" name="q" class="form-control"
           {{ 'placeholder="Search"' if not button }} >
  </div>
  <input type="hidden" name="check_keywords" value="yes" />
  <input type="hidden" name="area" value="default" />
  {% if button %}
    <input type="submit" class="btn btn-default" value="search">
  {% endif %}
</form>
{%- endmacro %}

{% block header %}
<nav class="navbar navbar-inverse">
  <div class="tb-container">
    <div class="row">
      <div class="navbar-content">
        {# Brand and toggle get grouped for better mobile display #}
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed"
                  data-toggle="collapse" data-target=".navbar-collapse">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="index.html">{fmt}</a>
        </div>

        {# Collect the nav links, forms, and other content for toggling #}
        <div class="collapse navbar-collapse">
          <ul class="nav navbar-nav">
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown"
                 role="button" aria-expanded="false">{{ version }}
                <span class="caret"></span></a>
              <ul class="dropdown-menu" role="menu">
                {% for v in versions.split(',') %}
                <li><a href="https://fmt.dev/{{v}}">{{v}}</a></li>
                {% endfor %}
              </ul>
            </li>
            {% for name in ['Contents', 'Usage', 'API', 'Syntax'] %}
              {% if pagename == name.lower() %}
              <li class="active"><a href="{{name.lower()}}.html">{{name}}
                <span class="sr-only">(current)</span></a></li>
              {%else%}
              <li><a href="{{name.lower()}}.html">{{name}}</a></li>
              {%endif%}
            {% endfor %}
          </ul>
          {% if pagename != 'search' %}
            {{ searchform('navbar-form navbar-right', False) }}
          {%endif%}
        </div> {# /.navbar-collapse #}
      </div> {# /.col-md-offset-2 #}
    </div> {# /.row #}
  </div> {# /.tb-container #}
</nav>
{% if pagename == "index" %}
{% set download_url = 'https://github.com/fmtlib/fmt/releases/download' %}
<div class="jumbotron">
  <div class="tb-container">
    <h1>{fmt}</h1>
    <p class="lead">A modern formatting library</p>
    <div class="btn-group" role="group">
      {% set name = 'fmt' if version.split('.')[0]|int >= 3 else 'cppformat' %}
      <a class="btn btn-success"
         href="{{download_url}}/{{version}}/{{name}}-{{version}}.zip">
           <span class="glyphicon glyphicon-download"></span> Download
      </a>
      <button type="button" class="btn btn-success dropdown-toggle"
        data-toggle="dropdown"><span class="caret"></span></button>
      <ul class="dropdown-menu">
      {% for v in versions.split(',') %}
      {% set name = 'fmt' if v.split('.')[0]|int >= 3 else 'cppformat' %}
        <li><a href="{{download_url}}/{{v}}/{{name}}-{{v}}.zip">Version {{v}}
          </a></li>
      {% endfor %}
      </ul>
    </div>
  </div>
</div>
{% endif %}
{% endblock %}

{# Disable relbars. #}
{% block relbar1 %}
{% endblock %}
{% block relbar2 %}
{% endblock %}

{% block content %}
<div class="tb-container">
  <div class="row">
    {# Sidebar is currently disabled.
    <div class="bs-sidebar">
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
          {%- block sidebarlogo %}
          {%- if logo %}
            <p class="logo"><a href="{{ pathto(master_doc) }}">
              <img class="logo" src="{{ pathto('_static/' + logo, 1) }}"
                   alt="Logo"/>
            </a></p>
          {%- endif %}
          {%- endblock %}
          {%- for sidebartemplate in sidebars %}
          {%- include sidebartemplate %}
          {%- endfor %}
        </div>
      </div>
    </div>
    #}

    <div class="content">
      {% block body %} {% endblock %}
    </div>
  </div>
</div>
{% endblock %}

{% block footer %}
{{ super() }}
{# Placed at the end of the document so the pages load faster. #}
<script src="_static/bootstrap.min.js"></script>
{% endblock %}
