# LSP Client Manual Testing Guide

## Overview

This guide provides comprehensive manual testing procedures for the SpectrumIDE LSP client integration. Follow these tests to validate LSP functionality and ensure proper Arabic/RTL text handling.

## Prerequisites

1. **Build Requirements:**
   - SpectrumIDE compiled with LSP client components
   - ALS server executable built and accessible
   - Test workspace with sample Alif files

2. **Test Environment Setup:**
   ```bash
   # Create test workspace
   mkdir test_workspace
   cd test_workspace
   
   # Create sample Alif files
   echo 'دالة اختبار() { اطبع("مرحبا") }' > test.alif
   echo 'صنف مثال { دالة _تهيئة_() {} }' > class_test.alif
   ```

## Test Categories

### 1. **LSP Client Lifecycle Tests**

#### Test 1.1: Client Initialization
**Objective:** Verify LSP client initializes correctly

**Steps:**
1. Launch SpectrumIDE
2. Check debug output for "SpectrumLspClient: Initializing LSP client"
3. Verify no error messages during startup

**Expected Results:**
- ✅ Client initializes without errors
- ✅ Singleton instance created successfully
- ✅ Default features enabled (completion, hover, diagnostics)

#### Test 1.2: Server Connection
**Objective:** Verify connection to ALS server

**Steps:**
1. Open a .alif file in SpectrumIDE
2. Monitor connection state changes in debug output
3. Wait for "Successfully connected to ALS server" message

**Expected Results:**
- ✅ Connection state: Disconnected → Connecting → Initializing → Connected
- ✅ Server capabilities received and parsed
- ✅ No connection timeouts or errors

#### Test 1.3: Graceful Shutdown
**Objective:** Verify clean shutdown process

**Steps:**
1. With LSP client connected, close SpectrumIDE
2. Monitor shutdown sequence in debug output
3. Verify ALS server process terminates

**Expected Results:**
- ✅ Clean shutdown sequence executed
- ✅ ALS server process terminated gracefully
- ✅ No resource leaks or hanging processes

### 2. **Process Management Tests**

#### Test 2.1: Server Startup
**Objective:** Verify ALS server starts correctly

**Steps:**
1. Ensure ALS server is not running
2. Open SpectrumIDE and load .alif file
3. Check process list for ALS server process

**Expected Results:**
- ✅ ALS server process starts automatically
- ✅ Process uses stdio communication
- ✅ Working directory set correctly

#### Test 2.2: Auto-Restart on Crash
**Objective:** Verify automatic restart functionality

**Steps:**
1. Connect to ALS server successfully
2. Manually kill ALS server process (Task Manager/kill command)
3. Continue using SpectrumIDE

**Expected Results:**
- ✅ Client detects server crash
- ✅ Automatic restart initiated within 2 seconds
- ✅ Connection restored successfully
- ✅ Editor functionality continues normally

#### Test 2.3: Health Monitoring
**Objective:** Verify periodic health checks

**Steps:**
1. Connect to ALS server
2. Monitor debug output for health check messages
3. Wait for at least 2 health check cycles (60 seconds)

**Expected Results:**
- ✅ Health checks occur every 30 seconds
- ✅ Server responds to health pings
- ✅ No false positive crash detections

### 3. **Arabic/RTL Text Tests**

#### Test 3.1: Basic Arabic Text Input
**Objective:** Verify Arabic text handling

**Steps:**
1. Open new .alif file
2. Type Arabic code: `دالة مرحبا() { اطبع("السلام عليكم") }`
3. Move cursor through text using arrow keys

**Expected Results:**
- ✅ Arabic text displays correctly (RTL)
- ✅ Cursor movement follows logical text order
- ✅ Text selection works properly
- ✅ No character corruption or display issues

#### Test 3.2: Mixed RTL/LTR Content
**Objective:** Verify mixed content handling

**Steps:**
1. Create file with mixed content:
   ```alif
   دالة calculateSum(x, y) {
       متغير result = x + y
       اطبع("Result: " + result)
       ارجع result
   }
   ```
2. Test cursor positioning and text selection

**Expected Results:**
- ✅ Mixed text displays correctly
- ✅ Cursor positioning accurate for both RTL and LTR
- ✅ Text selection spans correctly across mixed content
- ✅ Copy/paste preserves text direction

#### Test 3.3: Position Mapping Accuracy
**Objective:** Verify LSP position calculations

**Steps:**
1. Open Arabic code file
2. Place cursor at various positions
3. Trigger completion (if available) at different positions
4. Check debug output for position coordinates

**Expected Results:**
- ✅ Cursor positions map correctly to LSP coordinates
- ✅ No position calculation errors in debug output
- ✅ Features work at all cursor positions
- ✅ Multi-byte character handling correct

### 4. **Error Handling Tests**

#### Test 4.1: Server Not Found
**Objective:** Verify handling of missing ALS server

**Steps:**
1. Configure invalid ALS server path
2. Launch SpectrumIDE
3. Attempt to open .alif file

**Expected Results:**
- ✅ Clear error message displayed
- ✅ Graceful fallback to legacy components
- ✅ No application crashes
- ✅ User can continue working

#### Test 4.2: Connection Timeout
**Objective:** Verify timeout handling

**Steps:**
1. Block ALS server network/stdio communication
2. Attempt to connect
3. Wait for timeout period

**Expected Results:**
- ✅ Connection timeout detected (10 seconds)
- ✅ Appropriate error message shown
- ✅ Client returns to disconnected state
- ✅ Retry mechanism available

#### Test 4.3: Invalid Workspace
**Objective:** Verify workspace validation

**Steps:**
1. Configure non-existent workspace path
2. Initialize LSP client
3. Check error handling

**Expected Results:**
- ✅ Workspace validation fails gracefully
- ✅ Clear error message provided
- ✅ No undefined behavior
- ✅ Client remains in safe state

### 5. **Performance Tests**

#### Test 5.1: Large File Handling
**Objective:** Verify performance with large files

**Steps:**
1. Create large .alif file (>1MB, >10,000 lines)
2. Open in SpectrumIDE
3. Monitor memory usage and response times

**Expected Results:**
- ✅ File opens within reasonable time (<5 seconds)
- ✅ Memory usage remains stable
- ✅ Scrolling and editing remain responsive
- ✅ No performance degradation over time

#### Test 5.2: Rapid Text Changes
**Objective:** Verify handling of rapid edits

**Steps:**
1. Open medium-sized .alif file
2. Make rapid text changes (typing, deleting, pasting)
3. Monitor LSP communication and performance

**Expected Results:**
- ✅ All text changes processed correctly
- ✅ No message queue overflow
- ✅ Response times remain acceptable
- ✅ No synchronization issues

## Test Execution Checklist

### Pre-Test Setup
- [ ] ALS server built and accessible
- [ ] SpectrumIDE compiled with LSP client
- [ ] Test workspace prepared
- [ ] Debug logging enabled
- [ ] System resources monitored

### During Testing
- [ ] Document all observed behaviors
- [ ] Capture debug output for analysis
- [ ] Note performance characteristics
- [ ] Test edge cases and error conditions
- [ ] Verify Arabic text handling thoroughly

### Post-Test Analysis
- [ ] Review debug logs for errors
- [ ] Analyze performance metrics
- [ ] Document any issues found
- [ ] Verify all test criteria met
- [ ] Prepare test report

## Reporting Issues

When reporting issues, include:

1. **Environment Information:**
   - OS version and architecture
   - Qt version
   - SpectrumIDE build configuration
   - ALS server version

2. **Reproduction Steps:**
   - Exact steps to reproduce
   - Sample code/files used
   - Expected vs actual behavior

3. **Debug Information:**
   - Relevant debug output
   - Error messages
   - Performance measurements
   - Screenshots if applicable

4. **Impact Assessment:**
   - Severity level (Critical/High/Medium/Low)
   - Affected functionality
   - Workaround availability
   - User experience impact

## Success Criteria

The LSP client integration is considered successful when:

- ✅ All lifecycle tests pass without errors
- ✅ Process management works reliably
- ✅ Arabic/RTL text handling is accurate
- ✅ Error conditions handled gracefully
- ✅ Performance meets requirements
- ✅ No regressions in existing functionality
- ✅ User experience remains smooth and intuitive

---

**Document Version:** 1.0  
**Last Updated:** January 2025  
**Next Review:** After each milestone completion
