name: Publish documentation

# publish the documentation on every merge to develop branch
on:
  push:
    branches:
      - develop
    paths:
      - docs/mkdocs/**
      - docs/examples/**
  workflow_dispatch:

permissions:
  contents: read

# we don't want to have concurrent jobs, and we don't want to cancel running jobs to avoid broken publications
concurrency:
  group: documentation
  cancel-in-progress: false

jobs:
  publish_documentation:
    if: github.repository == 'nlohmann/json'
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3

      - name: Install and update PlantUML
        run: sudo apt-get update ; sudo apt-get install -y plantuml

      - name: Install virtual environment
        run: make install_venv -C docs/mkdocs

      - name: Build documentation
        run: make build -C docs/mkdocs

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/mkdocs/site
