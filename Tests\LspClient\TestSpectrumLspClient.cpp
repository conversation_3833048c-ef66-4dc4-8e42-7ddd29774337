#include <QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QTimer>
#include "SpectrumLspClient.h"

class TestSpectrumLspClient : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Core functionality tests
    void testSingletonPattern();
    void testInitialization();
    void testConnectionStates();
    void testFeatureManagement();
    void testErrorHandling();
    void testHealthMonitoring();

    // Process management tests
    void testServerStartup();
    void testServerShutdown();
    void testAutoRestart();
    void testProcessCrashRecovery();

    // Protocol tests
    void testJsonRpcCommunication();
    void testInitializeHandshake();
    void testMessageParsing();

    // RTL text position mapping tests
    void testRtlPositionMapping();
    void testMixedTextHandling();
    void testArabicTextProcessing();

private:
    QString m_mockServerPath;
    QTemporaryDir m_tempWorkspace;
    SpectrumLspClient* m_client;
};

void TestSpectrumLspClient::initTestCase()
{
    // Setup mock ALS server for testing
    m_mockServerPath = createMockServer();
    QVERIFY(!m_mockServerPath.isEmpty());
}

void TestSpectrumLspClient::cleanupTestCase()
{
    // Cleanup mock server
    if (!m_mockServerPath.isEmpty()) {
        QFile::remove(m_mockServerPath);
    }
}

void TestSpectrumLspClient::init()
{
    m_client = &SpectrumLspClient::instance();
    QVERIFY(m_client != nullptr);
}

void TestSpectrumLspClient::cleanup()
{
    if (m_client) {
        m_client->stop();
    }
}

void TestSpectrumLspClient::testSingletonPattern()
{
    // Test singleton behavior
    SpectrumLspClient& client1 = SpectrumLspClient::instance();
    SpectrumLspClient& client2 = SpectrumLspClient::instance();
    
    QCOMPARE(&client1, &client2);
    QCOMPARE(&client1, m_client);
}

void TestSpectrumLspClient::testInitialization()
{
    // Test successful initialization
    bool result = m_client->initialize(m_mockServerPath, m_tempWorkspace.path());
    QVERIFY(result);
    
    QCOMPARE(m_client->getWorkspaceRoot(), m_tempWorkspace.path());
    QCOMPARE(m_client->getConnectionState(), SpectrumLspClient::ConnectionState::Disconnected);
    
    // Test invalid server path
    bool invalidResult = m_client->initialize("/invalid/path", m_tempWorkspace.path());
    QVERIFY(!invalidResult);
}

void TestSpectrumLspClient::testConnectionStates()
{
    QSignalSpy stateSpy(m_client, &SpectrumLspClient::connectionStateChanged);
    
    // Initialize and start
    QVERIFY(m_client->initialize(m_mockServerPath, m_tempWorkspace.path()));
    QVERIFY(m_client->start());
    
    // Wait for state changes
    QVERIFY(stateSpy.wait(5000));
    
    // Verify state progression
    QVERIFY(stateSpy.count() >= 1);
    auto firstState = stateSpy.takeFirst().at(0).value<SpectrumLspClient::ConnectionState>();
    QCOMPARE(firstState, SpectrumLspClient::ConnectionState::Connecting);
}

void TestSpectrumLspClient::testFeatureManagement()
{
    // Test feature enable/disable
    m_client->setFeatureEnabled("completion", true);
    QVERIFY(m_client->isFeatureEnabled("completion"));
    
    m_client->setFeatureEnabled("completion", false);
    QVERIFY(!m_client->isFeatureEnabled("completion"));
    
    // Test unknown feature
    QVERIFY(!m_client->isFeatureEnabled("unknown_feature"));
}

void TestSpectrumLspClient::testRtlPositionMapping()
{
    // Test Arabic text position calculations
    QString arabicText = "مرحبا بك في محرر طيف";
    
    // TODO: Implement position mapping tests
    // This will be expanded when document synchronization is implemented
    
    QVERIFY(true); // Placeholder
}

// Mock server creation helper
QString TestSpectrumLspClient::createMockServer()
{
    // Create a simple mock server executable for testing
    QString mockPath = QDir::temp().filePath("mock_als_server");
    
#ifdef Q_OS_WIN
    mockPath += ".exe";
#endif
    
    // Create a simple script that responds to LSP initialize
    QFile mockFile(mockPath);
    if (mockFile.open(QIODevice::WriteOnly)) {
        // Write mock server implementation
        mockFile.write("#!/bin/bash\necho 'Mock ALS Server'\n");
        mockFile.close();
        mockFile.setPermissions(QFile::ReadOwner | QFile::WriteOwner | QFile::ExeOwner);
    }
    
    return mockPath;
}

QTEST_MAIN(TestSpectrumLspClient)
#include "TestSpectrumLspClient.moc"
