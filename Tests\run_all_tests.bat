@echo off
REM SpectrumIDE LSP Client - Automated Testing Script (Windows)
REM This script runs all tests in sequence and generates a comprehensive report

setlocal enabledelayedexpansion

REM Configuration
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set TEST_RESULTS_DIR=%SCRIPT_DIR%Results
set TEST_LOGS_DIR=%SCRIPT_DIR%Logs

REM Colors (limited in Windows batch)
set INFO=[INFO]
set SUCCESS=[SUCCESS]
set WARNING=[WARNING]
set ERROR=[ERROR]

echo %INFO% Starting SpectrumIDE LSP Client automated testing...

REM Initialize test environment
echo %INFO% Initializing test environment...
if not exist "%TEST_RESULTS_DIR%" mkdir "%TEST_RESULTS_DIR%"
if not exist "%TEST_LOGS_DIR%" mkdir "%TEST_LOGS_DIR%"
if not exist "%SCRIPT_DIR%TestData\Workspaces\test_workspace" mkdir "%SCRIPT_DIR%TestData\Workspaces\test_workspace"
if not exist "%SCRIPT_DIR%MockServers" mkdir "%SCRIPT_DIR%MockServers"

REM Set environment variables
set SPECTRUM_TEST_ROOT=%SCRIPT_DIR%
set ALS_SERVER_PATH=%PROJECT_ROOT%\als\build\Debug\als.exe
set QT_LOGGING_RULES=*.debug=true

echo %SUCCESS% Test environment initialized

REM Check prerequisites
echo %INFO% Checking prerequisites...
where qmake >nul 2>&1
if errorlevel 1 (
    echo %ERROR% qmake not found. Please install Qt development tools.
    exit /b 1
)

where cmake >nul 2>&1
if errorlevel 1 (
    echo %ERROR% cmake not found. Please install CMake.
    exit /b 1
)

where python >nul 2>&1
if errorlevel 1 (
    echo %ERROR% python not found. Please install Python 3.
    exit /b 1
)

echo %SUCCESS% All prerequisites satisfied

REM Create mock server
echo %INFO% Creating mock ALS server...
(
echo #!/usr/bin/env python3
echo import sys
echo import json
echo.
echo def read_message^(^):
echo     while True:
echo         line = sys.stdin.readline^(^)
echo         if line.startswith^('Content-Length:'^):
echo             length = int^(line.split^(':'^)[1].strip^(^)^)
echo             break
echo     sys.stdin.readline^(^)  # empty line
echo     content = sys.stdin.read^(length^)
echo     return json.loads^(content^)
echo.
echo def send_message^(message^):
echo     content = json.dumps^(message, ensure_ascii=False^)
echo     content_bytes = content.encode^('utf-8'^)
echo     header = f"Content-Length: {len^(content_bytes^)}\\r\\n\\r\\n"
echo     sys.stdout.write^(header^)
echo     sys.stdout.write^(content^)
echo     sys.stdout.flush^(^)
echo.
echo def main^(^):
echo     try:
echo         while True:
echo             message = read_message^(^)
echo             if message.get^("method"^) == "initialize":
echo                 response = {
echo                     "jsonrpc": "2.0",
echo                     "id": message["id"],
echo                     "result": {
echo                         "capabilities": {
echo                             "textDocumentSync": 1,
echo                             "completionProvider": {"triggerCharacters": ["."]},
echo                             "hoverProvider": True,
echo                             "definitionProvider": True
echo                         },
echo                         "serverInfo": {"name": "Mock ALS Server", "version": "1.0.0-test"}
echo                     }
echo                 }
echo                 send_message^(response^)
echo             elif message.get^("method"^) == "shutdown":
echo                 response = {"jsonrpc": "2.0", "id": message["id"], "result": None}
echo                 send_message^(response^)
echo                 break
echo     except ^(EOFError, KeyboardInterrupt^):
echo         pass
echo.
echo if __name__ == "__main__":
echo     main^(^)
) > "%SCRIPT_DIR%MockServers\mock_als_server.py"

echo %SUCCESS% Mock server created

REM Build ALS server
echo %INFO% Building ALS server...
cd /d "%PROJECT_ROOT%\als"

if not exist "build" mkdir build
cd build

cmake -DCMAKE_BUILD_TYPE=Debug .. > "%TEST_LOGS_DIR%\als_build.log" 2>&1
if errorlevel 1 (
    echo %ERROR% ALS server CMake configuration failed
    type "%TEST_LOGS_DIR%\als_build.log"
    exit /b 1
)

cmake --build . --config Debug >> "%TEST_LOGS_DIR%\als_build.log" 2>&1
if errorlevel 1 (
    echo %ERROR% ALS server build failed
    type "%TEST_LOGS_DIR%\als_build.log"
    exit /b 1
)

REM Check if ALS server was built
if exist "Debug\als.exe" (
    echo %SUCCESS% ALS server built successfully
    Debug\als.exe --version > "%TEST_LOGS_DIR%\als_version.log" 2>&1
    set ALS_SERVER_PATH=%PROJECT_ROOT%\als\build\Debug\als.exe
) else if exist "als.exe" (
    echo %SUCCESS% ALS server built successfully
    als.exe --version > "%TEST_LOGS_DIR%\als_version.log" 2>&1
    set ALS_SERVER_PATH=%PROJECT_ROOT%\als\build\als.exe
) else (
    echo %ERROR% ALS server executable not found
    exit /b 1
)

cd /d "%PROJECT_ROOT%"

REM Build SpectrumIDE
echo %INFO% Building SpectrumIDE...
cd /d "%PROJECT_ROOT%\Spectrum"

if not exist "build" mkdir build
cd build

qmake ..\Spectrum.pro CONFIG+=debug > "%TEST_LOGS_DIR%\spectrum_build.log" 2>&1
if errorlevel 1 (
    echo %ERROR% SpectrumIDE qmake configuration failed
    type "%TEST_LOGS_DIR%\spectrum_build.log"
    exit /b 1
)

nmake >> "%TEST_LOGS_DIR%\spectrum_build.log" 2>&1
if errorlevel 1 (
    REM Try with mingw32-make if nmake fails
    mingw32-make >> "%TEST_LOGS_DIR%\spectrum_build.log" 2>&1
    if errorlevel 1 (
        echo %ERROR% SpectrumIDE build failed
        type "%TEST_LOGS_DIR%\spectrum_build.log"
        exit /b 1
    )
)

REM Check if SpectrumIDE was built
if exist "debug\Spectrum.exe" (
    echo %SUCCESS% SpectrumIDE built successfully
    set SPECTRUM_EXE=%PROJECT_ROOT%\Spectrum\build\debug\Spectrum.exe
) else if exist "Spectrum.exe" (
    echo %SUCCESS% SpectrumIDE built successfully
    set SPECTRUM_EXE=%PROJECT_ROOT%\Spectrum\build\Spectrum.exe
) else (
    echo %ERROR% SpectrumIDE executable not found
    exit /b 1
)

cd /d "%PROJECT_ROOT%"

REM Run integration tests
echo %INFO% Running integration tests...

REM Test mock server
echo %INFO% Testing mock server...
echo {"jsonrpc":"2.0","id":1,"method":"initialize","params":{"processId":null,"rootUri":"file:///tmp","capabilities":{}}} | python "%SCRIPT_DIR%MockServers\mock_als_server.py" > "%TEST_RESULTS_DIR%\mock_server_test.log" 2>&1
if errorlevel 1 (
    echo %WARNING% Mock server test failed
    echo FAIL > "%TEST_RESULTS_DIR%\integration_tests_result.txt"
) else (
    echo %SUCCESS% Mock server test passed
)

REM Test real ALS server (if available)
if exist "%ALS_SERVER_PATH%" (
    echo %INFO% Testing real ALS server...
    echo {"jsonrpc":"2.0","id":1,"method":"initialize","params":{"processId":null,"rootUri":"file:///tmp","capabilities":{}}} | "%ALS_SERVER_PATH%" --stdio > "%TEST_RESULTS_DIR%\real_server_test.log" 2>&1
    if errorlevel 1 (
        echo %WARNING% Real ALS server test failed
    ) else (
        echo %SUCCESS% Real ALS server test passed
    )
    echo PASS > "%TEST_RESULTS_DIR%\integration_tests_result.txt"
) else (
    echo %WARNING% Real ALS server not found, skipping test
    echo PASS > "%TEST_RESULTS_DIR%\integration_tests_result.txt"
)

REM Run manual verification
echo %INFO% Running manual verification checks...

set FILES_OK=1

REM Check LSP client files
if not exist "%PROJECT_ROOT%\Source\LspClient\SpectrumLspClient.h" (
    echo %ERROR% Missing file: Source\LspClient\SpectrumLspClient.h
    set FILES_OK=0
)
if not exist "%PROJECT_ROOT%\Source\LspClient\SpectrumLspClient.cpp" (
    echo %ERROR% Missing file: Source\LspClient\SpectrumLspClient.cpp
    set FILES_OK=0
)
if not exist "%PROJECT_ROOT%\Source\LspClient\LspProcess.h" (
    echo %ERROR% Missing file: Source\LspClient\LspProcess.h
    set FILES_OK=0
)
if not exist "%PROJECT_ROOT%\Source\LspClient\LspProcess.cpp" (
    echo %ERROR% Missing file: Source\LspClient\LspProcess.cpp
    set FILES_OK=0
)

REM Check build artifacts
if not exist "%SPECTRUM_EXE%" (
    echo %ERROR% SpectrumIDE executable not found
    set FILES_OK=0
)
if not exist "%ALS_SERVER_PATH%" (
    echo %ERROR% ALS server executable not found
    set FILES_OK=0
)

if !FILES_OK! == 1 (
    echo %SUCCESS% Manual verification passed
    echo PASS > "%TEST_RESULTS_DIR%\manual_verification_result.txt"
) else (
    echo %ERROR% Manual verification failed
    echo FAIL > "%TEST_RESULTS_DIR%\manual_verification_result.txt"
)

REM Generate test report
echo %INFO% Generating test report...

(
echo # SpectrumIDE LSP Client Test Report
echo.
echo **Generated**: %date% %time%
echo **Platform**: Windows
echo.
echo ## Test Results Summary
echo.
echo ^| Test Category ^| Result ^| Details ^|
echo ^|---------------^|--------^|---------^|
if exist "%ALS_SERVER_PATH%" (
    echo ^| Build - ALS Server ^| ✅ PASS ^| ALS server executable ^|
) else (
    echo ^| Build - ALS Server ^| ❌ FAIL ^| ALS server executable ^|
)
if exist "%SPECTRUM_EXE%" (
    echo ^| Build - SpectrumIDE ^| ✅ PASS ^| SpectrumIDE executable ^|
) else (
    echo ^| Build - SpectrumIDE ^| ❌ FAIL ^| SpectrumIDE executable ^|
)
echo ^| Unit Tests ^| ⚠️ SKIP ^| Not implemented yet ^|
if exist "%TEST_RESULTS_DIR%\integration_tests_result.txt" (
    set /p INTEGRATION_RESULT=<"%TEST_RESULTS_DIR%\integration_tests_result.txt"
    if "!INTEGRATION_RESULT!" == "PASS" (
        echo ^| Integration Tests ^| ✅ PASS ^| LSP communication ^|
    ) else (
        echo ^| Integration Tests ^| ❌ FAIL ^| LSP communication ^|
    )
)
if exist "%TEST_RESULTS_DIR%\manual_verification_result.txt" (
    set /p MANUAL_RESULT=<"%TEST_RESULTS_DIR%\manual_verification_result.txt"
    if "!MANUAL_RESULT!" == "PASS" (
        echo ^| Manual Verification ^| ✅ PASS ^| File structure check ^|
    ) else (
        echo ^| Manual Verification ^| ❌ FAIL ^| File structure check ^|
    )
)
echo.
echo ## Build Information
echo.
echo ### ALS Server
echo ```
if exist "%TEST_LOGS_DIR%\als_version.log" (
    type "%TEST_LOGS_DIR%\als_version.log"
) else (
    echo Version information not available
)
echo ```
echo.
echo ### SpectrumIDE
echo - Executable: %SPECTRUM_EXE%
echo - Build log: Available in %TEST_LOGS_DIR%\spectrum_build.log
echo.
echo ## Test Logs
echo.
echo - ALS Build: %TEST_LOGS_DIR%\als_build.log
echo - SpectrumIDE Build: %TEST_LOGS_DIR%\spectrum_build.log
echo - Mock Server Test: %TEST_RESULTS_DIR%\mock_server_test.log
echo - Real Server Test: %TEST_RESULTS_DIR%\real_server_test.log
echo.
echo ## Next Steps
echo.
findstr "FAIL" "%TEST_RESULTS_DIR%\*_result.txt" >nul 2>&1
if errorlevel 1 (
    echo ✅ **All tests passed! Ready to proceed with Task 2.2: Process Management System.**
) else (
    echo ❌ **Some tests failed. Review the logs and fix issues before proceeding.**
)
) > "%TEST_RESULTS_DIR%\test_report.md"

echo %SUCCESS% Test report generated: %TEST_RESULTS_DIR%\test_report.md

REM Display summary
echo.
echo ==================== TEST SUMMARY ====================
type "%TEST_RESULTS_DIR%\test_report.md" | findstr /C:"Test Results Summary" /A:10
echo =======================================================
echo.

REM Final result
findstr "FAIL" "%TEST_RESULTS_DIR%\*_result.txt" >nul 2>&1
if errorlevel 1 (
    echo %SUCCESS% All tests passed successfully!
    echo %INFO% View detailed report: %TEST_RESULTS_DIR%\test_report.md
    exit /b 0
) else (
    echo %ERROR% Some tests failed. Check the test report for details.
    exit /b 1
)

endlocal
