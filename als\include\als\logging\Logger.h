/**
 * @file Logger.h
 * @brief Production-ready logging system for Alif Language Server
 * 
 * Adapted from Pyramid logging library for ALS-specific needs.
 * Provides thread-safe, configurable logging with file rotation,
 * structured logging, and performance optimizations.
 */

#pragma once

#include <string>
#include <memory>
#include <mutex>
#include <fstream>
#include <sstream>
#include <chrono>
#include <thread>
#include <unordered_map>
#include <cassert>

// Platform-specific includes for source location
#ifdef _MSC_VER
#include <intrin.h>
#define ALS_FUNCTION_NAME __FUNCTION__
#define ALS_DEBUG_BREAK() __debugbreak()
#elif defined(__GNUC__) || defined(__clang__)
#include <cstdlib>
#define ALS_FUNCTION_NAME __PRETTY_FUNCTION__
#define ALS_DEBUG_BREAK() __builtin_trap()
#else
#define ALS_FUNCTION_NAME __func__
#define ALS_DEBUG_BREAK() std::abort()
#endif

namespace als {
namespace logging {

// Log levels with priority ordering
enum class LogLevel : int {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Critical = 5,
    Off = 6
};

// Convert log level to string
const char* LogLevelToString(LogLevel level);

// Convert string to log level
LogLevel StringToLogLevel(const std::string& str);

// Source location information
struct SourceLocation {
    const char* file;
    const char* function;
    int line;

    SourceLocation(const char* file = __builtin_FILE(),
                   const char* function = nullptr,
                   int line = __builtin_LINE())
        : file(file), function(function ? function : "unknown"), line(line) {}
};

// Log entry structure for structured logging
struct LogEntry {
    LogLevel level;
    std::chrono::system_clock::time_point timestamp;
    std::thread::id threadId;
    SourceLocation location;
    std::string message;
    std::unordered_map<std::string, std::string> fields;
};

// Forward declaration
class Logger;

// Logger configuration
struct LoggerConfig {
    LogLevel consoleLevel = LogLevel::Info;
    LogLevel fileLevel = LogLevel::Debug;
    std::string logFilePath = "als.log";
    size_t maxFileSize = 10 * 1024 * 1024; // 10MB
    size_t maxFiles = 5;
    bool enableConsole = true;
    bool enableFile = true;
    bool enableTimestamp = true;
    bool enableThreadId = true;
    bool enableSourceLocation = true;
    std::string timestampFormat = "%Y-%m-%d %H:%M:%S";
};

// Thread-safe logger implementation
class Logger {
public:
    static Logger& GetInstance();

    // Configuration
    void Configure(const LoggerConfig& config);
    LoggerConfig GetConfig() const;

    // Core logging function
    void Log(LogLevel level, const std::string& message,
             const SourceLocation& location = SourceLocation{});

    // Structured logging
    void LogStructured(LogLevel level, const std::string& message,
                       const std::unordered_map<std::string, std::string>& fields,
                       const SourceLocation& location = SourceLocation{});

    // Template logging with variadic arguments
    template<typename... Args>
    void LogFormatted(LogLevel level, const SourceLocation& location, Args&&... args);

    // Flush all outputs
    void Flush();

    // Set log levels at runtime
    void SetConsoleLevel(LogLevel level);
    void SetFileLevel(LogLevel level);

    // Enable/disable outputs
    void EnableConsole(bool enable);
    void EnableFile(bool enable);

private:
    Logger() = default;
    ~Logger();

    // Non-copyable, non-movable
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    Logger(Logger&&) = delete;
    Logger& operator=(Logger&&) = delete;

    // Internal logging implementation
    void WriteToConsole(const LogEntry& entry);
    void WriteToFile(const LogEntry& entry);
    void RotateLogFile();
    std::string FormatLogEntry(const LogEntry& entry, bool includeColor = false);
    std::string GetTimestamp(const std::chrono::system_clock::time_point& time);
    std::string GetColorCode(LogLevel level);
    std::string GetResetColorCode();

    // Thread safety
    mutable std::mutex m_mutex;

    // Configuration
    LoggerConfig m_config;

    // File handling
    std::ofstream m_logFile;
    size_t m_currentFileSize = 0;
};

// Helper class for stream-style logging
class LogStream {
public:
    LogStream(LogLevel level, const SourceLocation& location);
    ~LogStream();

    template<typename T>
    LogStream& operator<<(const T& value) {
        m_stream << value;
        return *this;
    }

private:
    LogLevel m_level;
    SourceLocation m_location;
    std::ostringstream m_stream;
};

// Variadic template implementation for formatted logging
template<typename... Args>
void Logger::LogFormatted(LogLevel level, const SourceLocation& location, Args&&... args) {
    if (level < m_config.consoleLevel && level < m_config.fileLevel)
        return; // Early exit for performance

    // Build message without holding the lock to avoid deadlock
    std::ostringstream localBuffer;
    ((localBuffer << args), ...);

    // Now call Log with the formatted message
    Log(level, localBuffer.str(), location);
}

} // namespace logging
} // namespace als

// Enhanced logging macros for ALS
#define ALS_LOG_TRACE(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Trace, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)
#define ALS_LOG_DEBUG(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Debug, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)
#define ALS_LOG_INFO(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Info, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)
#define ALS_LOG_WARN(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Warn, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)
#define ALS_LOG_ERROR(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Error, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)
#define ALS_LOG_CRITICAL(...) ::als::logging::Logger::GetInstance().LogFormatted(::als::logging::LogLevel::Critical, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__}, __VA_ARGS__)

// Stream-style logging macros
#define ALS_LOG_STREAM(level) ::als::logging::LogStream(level, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__})
#define ALS_TRACE_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Trace)
#define ALS_DEBUG_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Debug)
#define ALS_INFO_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Info)
#define ALS_WARN_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Warn)
#define ALS_ERROR_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Error)
#define ALS_CRITICAL_STREAM() ALS_LOG_STREAM(::als::logging::LogLevel::Critical)

// Structured logging macros
#define ALS_LOG_STRUCTURED(level, message, fields) \
    ::als::logging::Logger::GetInstance().LogStructured(level, message, fields, ::als::logging::SourceLocation{__FILE__, ALS_FUNCTION_NAME, __LINE__})

// Conditional logging (only in debug builds)
#ifdef NDEBUG
#define ALS_LOG_DEBUG_ONLY(...) do { } while (0)
#define ALS_LOG_TRACE_ONLY(...) do { } while (0)
#else
#define ALS_LOG_DEBUG_ONLY(...) ALS_LOG_DEBUG(__VA_ARGS__)
#define ALS_LOG_TRACE_ONLY(...) ALS_LOG_TRACE(__VA_ARGS__)
#endif

// Enhanced assertion macros
#ifdef NDEBUG
#define ALS_ENABLE_ASSERTS 0
#else
#define ALS_ENABLE_ASSERTS 1
#endif

#if ALS_ENABLE_ASSERTS
#define ALS_INTERNAL_ASSERT(check, level, ...) \
    do { \
        if (!(check)) { \
            ALS_LOG_##level("Assertion Failed: ", __VA_ARGS__); \
            ALS_DEBUG_BREAK(); \
        } \
    } while (0)

// Client-side asserts - Log as ERROR
#define ALS_ASSERT(check, ...) ALS_INTERNAL_ASSERT(check, ERROR, __VA_ARGS__)

// Core engine asserts - Log as CRITICAL
#define ALS_CORE_ASSERT(check, ...) ALS_INTERNAL_ASSERT(check, CRITICAL, __VA_ARGS__)
#else
#define ALS_ASSERT(check, ...) do { } while (0)
#define ALS_CORE_ASSERT(check, ...) do { } while (0)
#endif

// Performance macros for high-frequency logging
#define ALS_LOG_IF(condition, level, ...) \
    do { \
        if (condition) { \
            ALS_LOG_##level(__VA_ARGS__); \
        } \
    } while (0)

// Logger configuration helper
#define ALS_CONFIGURE_LOGGER(config) \
    ::als::logging::Logger::GetInstance().Configure(config)

// Quick logger setup for common scenarios
#define ALS_SETUP_CONSOLE_LOGGING(level) \
    do { \
        ::als::logging::LoggerConfig config; \
        config.consoleLevel = ::als::logging::LogLevel::level; \
        config.enableFile = false; \
        ALS_CONFIGURE_LOGGER(config); \
    } while (0)

#define ALS_SETUP_FILE_LOGGING(level, filepath) \
    do { \
        ::als::logging::LoggerConfig config; \
        config.fileLevel = ::als::logging::LogLevel::level; \
        config.logFilePath = filepath; \
        config.enableConsole = false; \
        ALS_CONFIGURE_LOGGER(config); \
    } while (0)
