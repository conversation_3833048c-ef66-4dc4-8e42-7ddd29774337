// Copyright(c) 2015-present, <PERSON><PERSON> & spdlog contributors.
// Distributed under the MIT License (http://opensource.org/licenses/MIT)

#pragma once

// details/pattern_formatter-inl.h
// fmt/bin_to_hex.h
// fmt/bundled/format-inl.h
#include <cctype>

// details/file_helper-inl.h
// details/os-inl.h
// fmt/bundled/core.h
// fmt/bundled/posix.h
// logger-inl.h
// sinks/daily_file_sink.h
// sinks/stdout_sinks.h
#include <cstdio>

// details/os-inl.h
// fmt/bundled/posix.h
#include <cstdlib>

// details/os-inl.h
// details/pattern_formatter-inl.h
// fmt/bundled/core.h
// fmt/bundled/format-inl.h
#include <cstring>

// details/os-inl.h
// details/os.h
// details/pattern_formatter-inl.h
// details/pattern_formatter.h
// fmt/bundled/chrono.h
// sinks/daily_file_sink.h
// sinks/rotating_file_sink-inl.h
#include <ctime>

// fmt/bundled/format-inl.h
#include <climits>

// fmt/bundled/format-inl.h
#include <cwchar>

// fmt/bundled/format-inl.h
// fmt/bundled/format.h
#include <cmath>

// fmt/bundled/format-inl.h
#include <cstdarg>

// details/file_helper-inl.h
// fmt/bundled/format.h
// fmt/bundled/posix.h
// sinks/rotating_file_sink-inl.h
#include <cerrno>

// details/circular_q.h
// details/thread_pool-inl.h
// fmt/bundled/format-inl.h
#include <cassert>

// async_logger-inl.h
// cfg/helpers-inl.h
// log_levels.h
// common.h
// details/file_helper-inl.h
// details/log_msg.h
// details/os-inl.h
// details/pattern_formatter-inl.h
// details/pattern_formatter.h
// details/registry-inl.h
// details/registry.h
// details/tcp_client-windows.h
// details/tcp_client.h
// fmt/bundled/core.h
// sinks/android_sink.h
// sinks/ansicolor_sink.h
// sinks/basic_file_sink.h
// sinks/daily_file_sink.h
// sinks/dup_filter_sink.h
// sinks/msvc_sink.h
// sinks/ringbuffer_sink.h
// sinks/rotating_file_sink-inl.h
// sinks/rotating_file_sink.h
// sinks/syslog_sink.h
// sinks/tcp_sink.h
// sinks/win_eventlog_sink.h
// sinks/wincolor_sink.h
// spdlog.h:
#include <string>

// cfg/helpers-inl.h
// fmt/bundled/chrono.h
#include <sstream>

// fmt/bundled/ostream.h
// sinks/ostream_sink.h
#include <ostream>

// cfg/log_levels.h
// details/registry-inl.h
// details/registry.h
#include <unordered_map>

// details/circular_q.h
// details/pattern_formatter-inl.h
// details/pattern_formatter.h
// details/thread_pool.h
// fmt/bundled/compile.h
// logger.h
// sinks/dist_sink.h
// sinks/ringbuffer_sink.h
// sinks/win_eventlog_sink.h
#include <vector>

// details/os-inl.h
// details/pattern_formatter-inl.h
// sinks/ansicolor_sink.h
// sinks/syslog_sink.h
// sinks/systemd_sink.h
// sinks/wincolor_sink.h
#include <array>

// details/file_helper-inl.h
// details/file_helper.h
// sinks/rotating_file_sink-inl.h
#include <tuple>

// details/os-inl.h
// fmt/bundled/format.h
// fmt/bundled/printf.h
#include <limits>

// common.h
// details/backtracer.h
// details/null_mutex.h
#include <atomic>

// common.h
// details/backtracer.h
// details/null_mutex.h
#include <locale>

// common.h
#include <initializer_list>

// common.h
#include <exception>

// common.h
// details/fmt_helper.h
// fmt/bundled/core.h
// fmt/bundled/ranges.h
#include <type_traits>

// cfg/helpers-inl.h
// details/null_mutex.h
// details/pattern_formatter-inl.h
#include <utility>

// async.h
// async_logger-inl.h
// common.h
// details/pattern_formatter-inl.h
// details/pattern_formatter.h
// details/registry-inl.h
// details/registry.h
// details/thread_pool.h
// fmt/bundled/format.h
// sinks/ansicolor_sink.h
// sinks/base_sink-inl.h
// sinks/dist_sink.h
// sinks/stdout_sinks-inl.h
// sinks/wincolor_sink.h
// spdlog.h
#include <memory>

// async.h
// common.h
// details/backtracer.h
// details/periodic_worker.h
// details/registry-inl.h
// details/registry.h
// details/thread_pool.h
// sinks/tcp_sink.h
// spdlog.h
#include <functional>

// details/mpmc_blocking_q.h
// details/periodic_worker.h
#include <condition_variable>

// details/os-inl.h
// fmt/bundled/format.h
// fmt/bundled/printf.h
// sinks/dist_sink.h
#include <algorithm>

// common.h
// details/file_helper-inl.h
// details/fmt_helper.h
// details/os-inl.h
// details/pattern_formatter-inl.h
// details/pattern_formatter.h
// details/periodic_worker.h
// details/registry-inl.h
// details/registry.h
// details/thread_pool.h
// fmt/bundled/chrono.h
// sinks/android_sink.h
// sinks/daily_file_sink.h
// sinks/dup_filter_sink.h
// sinks/rotating_file_sink-inl.h
// sinks/rotating_file_sink.h
// sinks/tcp_sink.h
// spdlog.h
#include <chrono>

// details/file_helper-inl.h
// details/os-inl.h
// details/pattern_formatter-inl.h
// details/periodic_worker.h
// details/thread_pool.h
// sinks/android_sink.h
#include <thread>

// async.h
// details/backtracer.h
// details/console_globals.h
// details/mpmc_blocking_q.h
// details/pattern_formatter-inl.h
// details/periodic_worker.h
// details/registry.h
// sinks/android_sink.h
// sinks/ansicolor_sink.h
// sinks/basic_file_sink.h
// sinks/daily_file_sink.h
// sinks/dist_sink.h
// sinks/dup_filter_sink.h
// sinks/msvc_sink.h
// sinks/null_sink.h
// sinks/ostream_sink.h
// sinks/ringbuffer_sink.h
// sinks/rotating_file_sink-inl.h
// sinks/rotating_file_sink.h
// sinks/tcp_sink.h
// sinks/win_eventlog_sink.h
// sinks/wincolor_sink.h
//
// color_sinks.cpp
// file_sinks.cpp
// spdlog.cpp
// stdout_sinks.cpp
#include <mutex>

// spdlog
#include <spdlog/common.h>