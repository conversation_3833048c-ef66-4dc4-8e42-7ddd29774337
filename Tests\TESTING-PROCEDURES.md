# SpectrumIDE LSP Client - Testing Procedures (Part 2)

## 4.2 Run Integration Tests

### Execute Integration Tests
```bash
# Navigate to integration tests
cd Tests/Integration

# Build integration tests
qmake IntegrationTests.pro CONFIG+=debug
make

# Run with verbose output
./IntegrationTestRunner -v2 -o integration_results.xml,xunitxml

# Check results
echo "Integration test exit code: $?"

# View results
cat integration_results.xml
```

### Test with Real ALS Server (if available)
```bash
# First, ensure ALS server is built
cd ../../als/build
make

# Test ALS server independently
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"processId":null,"rootUri":"file:///tmp","capabilities":{}}}' | ./als --stdio
# Should respond with initialize result (Ctrl+C to exit)

# Run integration tests with real server
cd ../../Tests/Integration
export ALS_SERVER_PATH="../../als/build/als"
./IntegrationTestRunner -v2
```

## 5. Manual Testing

### 5.1 Debug Output Setup

#### Enable Debug Logging
```bash
# Create debug configuration
cat > Tests/debug_config.sh << 'EOF'
#!/bin/bash

# Set Qt debug environment
export QT_LOGGING_RULES="*.debug=true"
export QT_MESSAGE_PATTERN="[%{time yyyyMMdd h:mm:ss.zzz t} %{if-debug}D%{endif}%{if-warning}W%{endif}%{if-critical}C%{endif}%{if-fatal}F%{endif}] %{file}:%{line} - %{message}"

# Set LSP client debug flags
export SPECTRUM_LSP_DEBUG=1
export SPECTRUM_LSP_LOG_LEVEL=debug

echo "Debug environment configured"
echo "Qt logging: $QT_LOGGING_RULES"
echo "Message pattern: $QT_MESSAGE_PATTERN"
EOF

chmod +x Tests/debug_config.sh
```

#### Launch SpectrumIDE with Debug Output
```bash
# Source debug configuration
source Tests/debug_config.sh

# Launch SpectrumIDE from terminal to see debug output
cd Spectrum/build
./Spectrum 2>&1 | tee ../../Tests/Logs/spectrum_debug.log

# In another terminal, monitor the log file
tail -f Tests/Logs/spectrum_debug.log | grep -i lsp
```

### 5.2 Manual Test Procedures

#### Test 1: Basic LSP Client Lifecycle
```bash
# Expected debug output sequence:
# 1. "SpectrumLspClient: Initializing LSP client"
# 2. "LspProcess: Initializing process manager" 
# 3. "LspProtocol: Initializing JSON-RPC protocol handler"
# 4. "SpectrumLspClient: Core components initialized successfully"

# Steps:
# 1. Launch SpectrumIDE
# 2. Check debug output for initialization messages
# 3. Open a .alif file
# 4. Look for server connection messages
# 5. Close SpectrumIDE
# 6. Verify clean shutdown messages
```

#### Test 2: Process Management
```bash
# Test server startup:
# 1. Open .alif file
# 2. Check process list: ps aux | grep als
# 3. Verify ALS server process is running
# 4. Check debug output for "Server process started successfully"

# Test auto-restart:
# 1. Connect to ALS server successfully
# 2. Kill ALS server process: kill -9 <als_pid>
# 3. Continue using SpectrumIDE
# 4. Verify restart messages in debug output
# 5. Check that new ALS process starts

# Commands to monitor:
ps aux | grep als                    # Check ALS process
netstat -an | grep LISTEN           # Check listening ports (if using socket)
lsof -p <spectrum_pid>              # Check file descriptors
```

#### Test 3: Error Handling
```bash
# Test invalid server path:
# 1. Configure invalid ALS server path in code or environment
# 2. Launch SpectrumIDE
# 3. Open .alif file
# 4. Verify error message: "ALS server not found"
# 5. Check that application doesn't crash

# Test connection timeout:
# 1. Block ALS server (firewall or rename executable)
# 2. Try to connect
# 3. Wait for timeout (10 seconds)
# 4. Verify timeout error message
# 5. Check graceful fallback behavior
```

### 5.3 Verify LSP Communication

#### Monitor LSP Messages
```bash
# Create LSP message monitor script
cat > Tests/monitor_lsp.sh << 'EOF'
#!/bin/bash

# Monitor LSP communication by intercepting stdio
# This requires modifying LspProcess to log messages

echo "Monitoring LSP communication..."
echo "Look for these message types:"
echo "  - initialize request/response"
echo "  - initialized notification"
echo "  - textDocument/didOpen"
echo "  - textDocument/didChange"
echo "  - textDocument/completion"

# Watch debug log for LSP messages
tail -f Tests/Logs/spectrum_debug.log | grep -E "(Content-Length|jsonrpc|initialize|textDocument)"
EOF

chmod +x Tests/monitor_lsp.sh

# Run monitor in separate terminal
./Tests/monitor_lsp.sh
```

#### Verify JSON-RPC Protocol
```bash
# Expected message flow:
# 1. Client -> Server: initialize request
# 2. Server -> Client: initialize response with capabilities
# 3. Client -> Server: initialized notification
# 4. Client -> Server: textDocument/didOpen (when file opened)
# 5. Client -> Server: textDocument/didChange (when text edited)

# Look for these patterns in debug output:
grep "Content-Length:" Tests/Logs/spectrum_debug.log
grep "initialize" Tests/Logs/spectrum_debug.log
grep "capabilities" Tests/Logs/spectrum_debug.log
```

## 6. Arabic/RTL Text Testing

### 6.1 Setup Arabic Text Environment

#### Install Arabic Fonts (if needed)
```bash
# Linux
sudo apt-get install fonts-arabeyes fonts-kacst fonts-amiri

# macOS (fonts usually pre-installed)
# Check: fc-list | grep -i arab

# Windows (Arabic language pack should be installed)
# Check: Control Panel > Region > Languages
```

#### Create Arabic Test Files
```bash
# Create comprehensive Arabic test file
cat > Tests/TestData/arabic_comprehensive.alif << 'EOF'
// اختبار شامل للنص العربي في محرر طيف
// Comprehensive Arabic text test for Spectrum IDE

دالة اختبار_النص_العربي() {
    // متغيرات بأسماء عربية
    متغير اسم_المستخدم = "أحمد محمد"
    متغير العمر = ٢٥
    متغير الراتب = ٥٠٠٠.٧٥
    
    // نص مختلط عربي وإنجليزي
    متغير mixed_text = "Hello مرحبا World عالم"
    
    // أرقام عربية وإنجليزية
    متغير arabic_numbers = [١، ٢، ٣، ٤، ٥]
    متغير english_numbers = [1, 2, 3, 4, 5]
    
    // جمل شرطية
    اذا (العمر > ١٨) {
        اطبع("بالغ")
    } والا {
        اطبع("قاصر")
    }
    
    // حلقات تكرار
    لاجل عدد في arabic_numbers {
        اطبع("الرقم العربي: " + عدد)
    }
    
    // استدعاء دالة مع معاملات عربية
    نتيجة_الحساب = حساب_المجموع(١٠، ٢٠)
    
    ارجع نتيجة_الحساب
}

دالة حساب_المجموع(العدد_الأول، العدد_الثاني) {
    متغير المجموع = العدد_الأول + العدد_الثاني
    اطبع("مجموع " + العدد_الأول + " و " + العدد_الثاني + " = " + المجموع)
    ارجع المجموع
}

// صنف بأسماء عربية
صنف الطالب {
    دالة _تهيئة_(الاسم، العمر، الدرجات) {
        هذا.الاسم = الاسم
        هذا.العمر = العمر  
        هذا.الدرجات = الدرجات
    }
    
    دالة حساب_المعدل() {
        متغير مجموع_الدرجات = ٠
        لاجل درجة في هذا.الدرجات {
            مجموع_الدرجات += درجة
        }
        ارجع مجموع_الدرجات / طول(هذا.الدرجات)
    }
    
    دالة عرض_المعلومات() {
        اطبع("الاسم: " + هذا.الاسم)
        اطبع("العمر: " + هذا.العمر)
        اطبع("المعدل: " + هذا.حساب_المعدل())
    }
}

// اختبار الصنف
متغير طالب١ = جديد الطالب("فاطمة أحمد", ٢٠, [٩٠، ٨٥، ٩٢، ٨٨])
طالب١.عرض_المعلومات()
EOF
```

### 6.2 Arabic Text Testing Procedures

#### Test 1: Basic Arabic Text Display
```bash
# Steps:
# 1. Open Tests/TestData/arabic_comprehensive.alif in SpectrumIDE
# 2. Verify Arabic text displays correctly (right-to-left)
# 3. Check that Arabic and English text are properly aligned
# 4. Verify Arabic numbers (١٢٣) display correctly
# 5. Check mixed RTL/LTR content alignment

# Expected behavior:
# ✅ Arabic text flows right-to-left
# ✅ English text flows left-to-right within Arabic context
# ✅ Numbers display in correct direction
# ✅ Punctuation appears in correct positions
# ✅ Line breaks work correctly with RTL text
```

#### Test 2: Cursor Movement and Selection
```bash
# Steps:
# 1. Place cursor at beginning of Arabic line
# 2. Use arrow keys to move through text
# 3. Test Home/End keys
# 4. Test word-by-word movement (Ctrl+Arrow)
# 5. Test text selection with mouse and keyboard

# Expected behavior:
# ✅ Left arrow moves to next character (logically)
# ✅ Right arrow moves to previous character (logically)  
# ✅ Home/End work correctly with RTL text
# ✅ Word boundaries detected correctly for Arabic
# ✅ Selection highlights correct text regions
```

#### Test 3: Editing Operations
```bash
# Steps:
# 1. Insert text in middle of Arabic line
# 2. Delete characters using Backspace/Delete
# 3. Cut/Copy/Paste Arabic text
# 4. Test undo/redo with Arabic text
# 5. Test find/replace with Arabic text

# Expected behavior:
# ✅ Text insertion maintains correct direction
# ✅ Deletion works in logical order
# ✅ Copy/paste preserves text direction
# ✅ Undo/redo works correctly
# ✅ Find/replace handles Arabic correctly
```

#### Test 4: LSP Position Mapping
```bash
# This test requires LSP functionality to be working
# Steps:
# 1. Place cursor at various positions in Arabic text
# 2. Trigger completion (when implemented)
# 3. Check debug output for position coordinates
# 4. Verify positions map correctly between Qt and LSP

# Monitor debug output for position calculations:
grep -i "position\|cursor\|offset" Tests/Logs/spectrum_debug.log

# Expected behavior:
# ✅ Cursor positions calculated correctly
# ✅ No position mapping errors in debug output
# ✅ LSP requests use correct positions
# ✅ Multi-byte character handling works
```
