name: windows

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ${{matrix.os}}
    strategy:
      matrix:
        # windows-2019 has MSVC 2019 installed;
        # windows-2022 has MSVC 2022 installed:
        # https://github.com/actions/virtual-environments.
        os: [windows-2019]
        platform: [Win32, x64]
        toolset: [v140, v141, v142]
        standard: [14, 17, 20]
        shared: ["", -DBUILD_SHARED_LIBS=ON]
        build_type: [Debug, Release]
        exclude:
          - { toolset: v140, standard: 17 }
          - { toolset: v140, standard: 20 }
          - { toolset: v141, standard: 14 }
          - { toolset: v141, standard: 20 }
          - { toolset: v142, standard: 14 }
          - { platform: Win32, toolset: v140 }
          - { platform: Win32, toolset: v141 }
          - { platform: Win32, standard: 14 }
          - { platform: Win32, standard: 20 }
          - { platform: x64, toolset: v140, shared: -DBUILD_SHARED_LIBS=ON }
          - { platform: x64, toolset: v141, shared: -<PERSON><PERSON><PERSON>_SHARED_LIBS=ON }
          - { platform: x64, standard: 14, shared: -DBUILD_SHARED_LIBS=ON }
          - { platform: x64, standard: 20, shared: -DBUILD_SHARED_LIBS=ON }
        include:
          - os: windows-2022
            platform: x64
            toolset: v143
            build_type: Debug
            standard: 20

    steps:
    - uses: actions/checkout@c85c95e3d7251135ab7dc9ce3241c5835cc595a9 # v3.5.3

    - name: Set timezone
      run: tzutil /s "Ekaterinburg Standard Time"

    - name: Create Build Environment
      run: cmake -E make_directory ${{runner.workspace}}/build

    - name: Configure
      # Use a bash shell for $GITHUB_WORKSPACE.
      shell: bash
      working-directory: ${{runner.workspace}}/build
      run: |
        cmake -A ${{matrix.platform}} -T ${{matrix.toolset}} \
              -DCMAKE_CXX_STANDARD=${{matrix.standard}} \
              ${{matrix.shared}} -DCMAKE_BUILD_TYPE=${{matrix.build_type}} \
              $GITHUB_WORKSPACE

    - name: Build
      working-directory: ${{runner.workspace}}/build
      run: |
        $threads = (Get-CimInstance Win32_ComputerSystem).NumberOfLogicalProcessors
        cmake --build . --config ${{matrix.build_type}} --parallel $threads

    - name: Test
      working-directory: ${{runner.workspace}}/build
      run: ctest -C ${{matrix.build_type}} -V
      env:
        CTEST_OUTPUT_ON_FAILURE: True

  mingw:
    runs-on: windows-latest
    defaults:
      run:
        shell: msys2 {0}
    strategy:
      matrix:
        sys: [ mingw64, ucrt64 ]
    steps:
    - name: Set timezone
      run: tzutil /s "Ekaterinburg Standard Time"
      shell: cmd
    - uses: msys2/setup-msys2@7efe20baefed56359985e327d329042cde2434ff # v2
      with:
        release: false
        msystem: ${{matrix.sys}}
        pacboy: cc:p cmake:p ninja:p lld:p
    - uses: actions/checkout@c85c95e3d7251135ab7dc9ce3241c5835cc595a9 # v3.5.3
    - name: Configure
      run: cmake -B ../build -DBUILD_SHARED_LIBS=ON -DCMAKE_BUILD_TYPE=Debug
      env: { LDFLAGS: -fuse-ld=lld }
    - name: Build
      run: cmake --build ../build
    - name: Test
      run: ctest -j `nproc` --test-dir ../build
      env:
        CTEST_OUTPUT_ON_FAILURE: True
