#include <QtTest>
#include <QApplication>
#include <QTemporaryDir>
#include <QTextStream>
#include "SpectrumLspClient.h"
#include "SPEditor.h"

class TestLspIntegration : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Integration tests
    void testEditorLspConnection();
    void testDocumentSynchronization();
    void testCompletionIntegration();
    void testArabicTextHandling();
    void testRealTimeEditing();
    void testMultipleDocuments();

    // Performance tests
    void testLargeFileHandling();
    void testResponseTimes();
    void testMemoryUsage();

private:
    void createTestAlifFile(const QString& path, const QString& content);
    void waitForLspReady(int timeout = 10000);
    
private:
    QString m_alsServerPath;
    QTemporaryDir m_workspace;
    SpectrumLspClient* m_lspClient;
    SPEditor* m_editor;
};

void TestLspIntegration::initTestCase()
{
    // Find ALS server executable
    m_alsServerPath = findALSServer();
    QVERIFY2(!m_alsServerPath.isEmpty(), "ALS server not found. Build ALS first.");
    
    // Create test workspace
    QVERIFY(m_workspace.isValid());
    
    // Create test Alif files
    createTestAlifFile(m_workspace.filePath("test.alif"), 
        "دالة اختبار() {\n"
        "    اطبع(\"مرحبا بالعالم\")\n"
        "    متغير عدد = 42\n"
        "    ارجع عدد\n"
        "}\n");
}

void TestLspIntegration::cleanupTestCase()
{
    if (m_lspClient) {
        m_lspClient->stop();
    }
}

void TestLspIntegration::init()
{
    m_lspClient = &SpectrumLspClient::instance();
    m_editor = new SPEditor();
    
    // Initialize LSP client
    QVERIFY(m_lspClient->initialize(m_alsServerPath, m_workspace.path()));
    QVERIFY(m_lspClient->start());
    
    waitForLspReady();
}

void TestLspIntegration::cleanup()
{
    delete m_editor;
    m_editor = nullptr;
    
    if (m_lspClient) {
        m_lspClient->stop();
    }
}

void TestLspIntegration::testEditorLspConnection()
{
    // Test that editor can connect to LSP client
    QVERIFY(m_lspClient->isConnected());
    
    // Test server capabilities
    auto capabilities = m_lspClient->getServerCapabilities();
    QVERIFY(capabilities.textDocumentSync);
    
    // Test feature availability
    QVERIFY(m_lspClient->isFeatureEnabled("completion"));
}

void TestLspIntegration::testDocumentSynchronization()
{
    // Open test file in editor
    QString testFile = m_workspace.filePath("test.alif");
    QFile file(testFile);
    QVERIFY(file.open(QIODevice::ReadOnly));
    
    QString content = file.readAll();
    m_editor->setPlainText(content);
    
    // TODO: Test document sync with LSP server
    // This will be implemented when document manager is complete
    
    QVERIFY(true); // Placeholder
}

void TestLspIntegration::testArabicTextHandling()
{
    // Test Arabic text input and processing
    QString arabicCode = "دالة مرحبا() {\n    اطبع(\"السلام عليكم\")\n}";
    m_editor->setPlainText(arabicCode);
    
    // Test cursor position with RTL text
    QTextCursor cursor = m_editor->textCursor();
    cursor.movePosition(QTextCursor::End);
    
    // Verify position calculations work correctly
    QVERIFY(cursor.position() > 0);
    
    // TODO: Test LSP position mapping with Arabic text
    QVERIFY(true); // Placeholder
}

void TestLspIntegration::testRealTimeEditing()
{
    // Test real-time editing with LSP synchronization
    m_editor->setPlainText("دالة ");
    
    // Simulate typing
    QTest::keyClicks(m_editor, "اختبار");
    
    // Wait for LSP processing
    QTest::qWait(100);
    
    // Verify text was processed
    QVERIFY(m_editor->toPlainText().contains("اختبار"));
}

void TestLspIntegration::createTestAlifFile(const QString& path, const QString& content)
{
    QFile file(path);
    QVERIFY(file.open(QIODevice::WriteOnly));
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    stream << content;
}

void TestLspIntegration::waitForLspReady(int timeout)
{
    QSignalSpy readySpy(m_lspClient, &SpectrumLspClient::serverReady);
    
    if (!m_lspClient->isConnected()) {
        QVERIFY2(readySpy.wait(timeout), "LSP server failed to become ready");
    }
}

QString TestLspIntegration::findALSServer()
{
    // Look for ALS server in common locations
    QStringList searchPaths = {
        "../als/build/Debug/als.exe",
        "../als/build/Release/als.exe", 
        "../als/build/als",
        "./als/als.exe",
        "./als/als"
    };
    
    for (const QString& path : searchPaths) {
        if (QFile::exists(path)) {
            return QFileInfo(path).absoluteFilePath();
        }
    }
    
    return QString();
}

QTEST_MAIN(TestLspIntegration)
#include "TestLspIntegration.moc"
