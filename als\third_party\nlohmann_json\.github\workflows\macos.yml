name: macOS

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  workflow_dispatch:

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  xcode_1:
    runs-on: macos-11
    strategy:
      matrix:
        xcode: ['11.7', '12.4', '12.5.1', '13.0']
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.xcode }}.app/Contents/Developer

    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -D CMAKE_BUILD_TYPE=Debug -DJSON_BuildTests=On -DJSON_FastTests=ON
      - name: Build
        run: cmake --build build --parallel 10
      - name: Test
        run: cd build ; ctest -j 10 --output-on-failure

  xcode_2:
    runs-on: macos-12
    strategy:
      matrix:
        xcode: ['13.1', '13.2.1', '13.3.1', '13.4.1', '14.0', '14.0.1', '14.1']
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.xcode }}.app/Contents/Developer

    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -D CMAKE_BUILD_TYPE=Debug -DJSON_BuildTests=On -DJSON_FastTests=ON
      - name: Build
        run: cmake --build build --parallel 10
      - name: Test
        run: cd build ; ctest -j 10 --output-on-failure

  xcode_standards:
    runs-on: macos-latest
    strategy:
      matrix:
        standard: [11, 14, 17, 20, 23]

    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -D CMAKE_BUILD_TYPE=Debug -DJSON_BuildTests=On -DJSON_TestStandards=${{ matrix.standard }}
      - name: Build
        run: cmake --build build --parallel 10
      - name: Test
        run: cd build ; ctest -j 10 --output-on-failure
