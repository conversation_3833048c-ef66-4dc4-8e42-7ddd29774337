#/usr/bin/env python3
import sys
import json

def read_message():
    while True:
        line = sys.stdin.readline()
        if line.startswith('Content-Length:'):
            length = int(line.split(':')[1].strip())
            break
    sys.stdin.readline()  # empty line
    content = sys.stdin.read(length)
    return json.loads(content)

def send_message(message):
    content = json.dumps(message, ensure_ascii=False)
    content_bytes = content.encode('utf-8')
    header = f"Content-Length: {len^(content_bytes^)}\\r\\n\\r\\n"
    sys.stdout.write(header)
    sys.stdout.write(content)
    sys.stdout.flush()

def main():
    try:
        while True:
            message = read_message()
            if message.get("method") == "initialize":
                response = {
                    "jsonrpc": "2.0",
                    "id": message["id"],
                    "result": {
                        "capabilities": {
                            "textDocumentSync": 1,
                            "completionProvider": {"triggerCharacters": ["."]},
                            "hoverProvider": True,
                            "definitionProvider": True
                        },
                        "serverInfo": {"name": "Mock ALS Server", "version": "1.0.0-test"}
                    }
                }
                send_message(response)
            elif message.get("method") == "shutdown":
                response = {"jsonrpc": "2.0", "id": message["id"], "result": None}
                send_message(response)
                break
    except (EOFError, KeyboardInterrupt):
        pass

if __name__ == "__main__":
    main()
