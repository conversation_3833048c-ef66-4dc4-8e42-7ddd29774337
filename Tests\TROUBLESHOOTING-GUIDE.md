# SpectrumIDE LSP Client - Troubleshooting Guide

## 7. Troubleshooting

### 7.1 Common Build Issues

#### Issue: "LspClient headers not found"
```bash
# Solution: Check include paths
grep -n INCLUDEPATH Spectrum/Spectrum.pro
# Should include ../Source/LspClient

# Verify files exist
ls -la Source/LspClient/
# Should show all .h and .cpp files

# Check file permissions
chmod 644 Source/LspClient/*
```

#### Issue: "Qt MOC errors"
```bash
# Solution: Clean and rebuild MOC files
cd Spectrum/build
rm -f moc_*.cpp moc_*.h
make clean
qmake ../Spectrum.pro
make

# Check Q_OBJECT macros
grep -n "Q_OBJECT" ../Source/LspClient/*.h
# Should show Q_OBJECT in class declarations
```

#### Issue: "Undefined reference to LSP symbols"
```bash
# Solution: Check linking
nm Spectrum | grep -i lsp
# Should show LSP symbols

# Verify source files in project
grep -A 10 "SOURCES" Spectrum.pro
# Should include all LspClient .cpp files
```

### 7.2 Runtime Issues

#### Issue: "ALS server not found"
```bash
# Solution: Check server path and permissions
ls -la als/build/als*
chmod +x als/build/als

# Test server independently
./als/build/als --version

# Check environment variables
echo $ALS_SERVER_PATH
export ALS_SERVER_PATH="$(pwd)/als/build/als"
```

#### Issue: "Connection timeout"
```bash
# Solution: Debug server startup
strace -f ./als/build/als --stdio 2>&1 | head -50
# Check for file access errors

# Monitor server process
ps aux | grep als
# Verify process starts and stays running

# Check server logs
./als/build/als --stdio --log-level debug
```

#### Issue: "Arabic text not displaying correctly"
```bash
# Solution: Check font and locale settings
fc-list | grep -i arab  # Linux
# Install Arabic fonts if missing

# Check Qt locale
export LC_ALL=ar_SA.UTF-8  # or appropriate locale
export LANG=ar_SA.UTF-8

# Verify Unicode support
python3 -c "print('مرحبا بالعالم')"
# Should display Arabic correctly
```

### 7.3 Debug Information Collection

#### Collect System Information
```bash
# Create system info script
cat > Tests/collect_debug_info.sh << 'EOF'
#!/bin/bash

echo "=== System Information ==="
uname -a
echo

echo "=== Qt Information ==="
qmake --version
echo

echo "=== Compiler Information ==="
gcc --version 2>/dev/null || clang --version
echo

echo "=== LSP Client Files ==="
find Source/LspClient -name "*.h" -o -name "*.cpp" | sort
echo

echo "=== Build Status ==="
ls -la Spectrum/build/Spectrum* 2>/dev/null || echo "SpectrumIDE not built"
ls -la als/build/als* 2>/dev/null || echo "ALS server not built"
echo

echo "=== Recent Debug Log ==="
tail -50 Tests/Logs/spectrum_debug.log 2>/dev/null || echo "No debug log found"
EOF

chmod +x Tests/collect_debug_info.sh
./Tests/collect_debug_info.sh > Tests/debug_info.txt
```

#### Performance Monitoring
```bash
# Monitor memory usage
cat > Tests/monitor_performance.sh << 'EOF'
#!/bin/bash

echo "Monitoring SpectrumIDE performance..."
echo "PID | Memory | CPU | Command"

while true; do
    ps aux | grep -E "(Spectrum|als)" | grep -v grep | awk '{printf "%s | %s | %s | %s\n", $2, $6, $3, $11}'
    sleep 5
done
EOF

chmod +x Tests/monitor_performance.sh
# Run in separate terminal
./Tests/monitor_performance.sh
```

### 7.4 Getting Help

#### Create Bug Report Template
```bash
cat > Tests/bug_report_template.md << 'EOF'
# Bug Report

## Environment
- OS: [Windows/Linux/macOS version]
- Qt Version: [from qmake --version]
- Compiler: [GCC/Clang/MSVC version]
- SpectrumIDE Version: [git commit hash]

## Problem Description
[Describe the issue clearly]

## Steps to Reproduce
1. [First step]
2. [Second step]
3. [Third step]

## Expected Behavior
[What should happen]

## Actual Behavior
[What actually happens]

## Debug Information
```
[Paste relevant debug output]
```

## Additional Context
[Any other relevant information]
EOF
```

---

## Quick Start Testing Checklist

### ✅ **Phase 1: Environment Setup (30 minutes)**
```bash
# 1. Install dependencies
sudo apt-get install qt5-qmake qt5-default cmake build-essential

# 2. Create test directories
mkdir -p Tests/{Unit,Integration,Manual,Results,Logs}

# 3. Set environment variables
export SPECTRUM_TEST_ROOT="$(pwd)/Tests"
export ALS_SERVER_PATH="$(pwd)/als/build/als"

# 4. Create test workspace
mkdir -p Tests/TestData/Workspaces/test_workspace
```

### ✅ **Phase 2: Build Verification (20 minutes)**
```bash
# 1. Build ALS server
cd als && mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Debug ..
make -j4
./als --version  # Should show version

# 2. Build SpectrumIDE
cd ../../Spectrum && mkdir build && cd build
qmake ../Spectrum.pro CONFIG+=debug
make -j4
ls -la Spectrum*  # Should show executable
```

### ✅ **Phase 3: Unit Testing (15 minutes)**
```bash
# 1. Create mock server
python3 Tests/MockServers/mock_als_server.py &
MOCK_PID=$!

# 2. Run unit tests
cd Tests/Unit
qmake UnitTests.pro && make
./UnitTestRunner -v2

# 3. Check results
echo "Exit code: $?"
kill $MOCK_PID
```

### ✅ **Phase 4: Integration Testing (20 minutes)**
```bash
# 1. Run integration tests
cd Tests/Integration
qmake IntegrationTests.pro && make
./IntegrationTestRunner -v2

# 2. Test with real ALS server
export ALS_SERVER_PATH="../../als/build/als"
./IntegrationTestRunner
```

### ✅ **Phase 5: Manual Testing (30 minutes)**
```bash
# 1. Enable debug output
source Tests/debug_config.sh

# 2. Launch SpectrumIDE
cd Spectrum/build
./Spectrum 2>&1 | tee ../../Tests/Logs/spectrum_debug.log &

# 3. Test basic functionality
# - Open .alif file
# - Check debug output
# - Verify LSP client initialization

# 4. Test Arabic text
# - Open Tests/TestData/arabic_comprehensive.alif
# - Test cursor movement
# - Verify RTL display
```

### ✅ **Phase 6: Results Analysis (10 minutes)**
```bash
# 1. Collect debug information
./Tests/collect_debug_info.sh

# 2. Check test results
grep -E "(PASS|FAIL|ERROR)" Tests/Results/*

# 3. Verify LSP communication
grep -i "initialize\|capabilities" Tests/Logs/spectrum_debug.log

# 4. Generate test report
echo "Testing completed at $(date)" > Tests/test_report.txt
echo "Build status: $(ls Spectrum/build/Spectrum* >/dev/null 2>&1 && echo 'SUCCESS' || echo 'FAILED')" >> Tests/test_report.txt
echo "Unit tests: $(grep -c PASS Tests/Results/* 2>/dev/null || echo 0) passed" >> Tests/test_report.txt
```

---

## Expected Results Summary

### ✅ **Successful Test Run Should Show:**

**Build Phase:**
- SpectrumIDE executable created without errors
- ALS server executable responds to `--version`
- All LSP client source files compile successfully

**Unit Tests:**
- All test classes pass (TestSpectrumLspClient, TestLspProcess, TestLspProtocol)
- No memory leaks or crashes
- Singleton pattern works correctly

**Integration Tests:**
- LSP client connects to mock server
- Initialize handshake completes successfully
- Server capabilities received and parsed

**Manual Tests:**
- SpectrumIDE launches without errors
- Debug output shows LSP client initialization
- Arabic text displays correctly in editor
- Process management works (server starts/stops)

**Debug Output Should Include:**
```
SpectrumLspClient: Initializing LSP client
LspProcess: Initializing process manager
LspProtocol: Initializing JSON-RPC protocol handler
SpectrumLspClient: Core components initialized successfully
LspProcess: Server process started successfully, PID: [number]
LspProtocol: Sending initialize request
LspProtocol: Received initialize response
SpectrumLspClient: Successfully connected to ALS server
```

### ❌ **Common Failure Indicators:**

**Build Failures:**
- "No such file or directory" for LSP headers
- "Undefined reference" to LSP symbols
- Qt MOC compilation errors

**Runtime Failures:**
- "ALS server not found" errors
- Connection timeout messages
- Segmentation faults or crashes
- Arabic text displaying as boxes or incorrect characters

**Test Failures:**
- Unit tests failing with assertion errors
- Integration tests timing out
- Mock server not responding

---

## Next Steps After Testing

### If All Tests Pass ✅
1. **Proceed to Task 2.2**: Process Management System implementation
2. **Begin Document Synchronization**: Start implementing document sync features
3. **Plan Integration**: Prepare for SPEditor integration

### If Tests Fail ❌
1. **Review Debug Output**: Check logs for specific error messages
2. **Fix Build Issues**: Resolve compilation or linking problems
3. **Debug Runtime Issues**: Use debugger to trace execution
4. **Seek Help**: Use bug report template to get assistance

This comprehensive testing approach ensures that the LSP client foundation is solid before proceeding with advanced features. Each phase builds confidence in the implementation and identifies issues early in the development process.
